<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>张展伟的简历</title>
  <link rel="stylesheet" href="http://0.0.0.0:18080/static/fontawesome/css/all.min.css" />
  <style>
    :root {
      --theme-color: #2E75B6;
      --base-font-size: 11pt;
      --max-font-size: 13pt;
      --spacing: 1.5;
      --text-color: #000000;
      --secondary-text-color: #555555;
      --border-color: #e0e0e0;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      /* font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; */
      /* font-family: 'Alibaba PuHuiTi', 'Noto Sans SC'; */
    }

    body {
      background-color: #f0f0f0;
      display: flex;
      justify-content: center;
      padding: 0px 0;
      overflow-x: hidden;
      font-family: "方正黑体简体", "Source Han Sans SC VF", sans-serif;
    }

    .resume-container {
      width: 210mm;
      /* min-height: 297mm; */
      background-color: white;
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);
      display: flex;
      flex-direction: column;
      overflow: hidden;
      font-size: var(--base-font-size);
      line-height: var(--spacing);
      color: var(--text-color);
    }

    /* Header Section */
    .resume-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: var(--theme-color);
      padding: 15px 30px;
      color: white;
    }

    .resume-header-title {
      font-size: calc(var(--base-font-size) * 2);
      font-weight: bold;
      letter-spacing: 2px;
    }

    .resume-header-icons {
      display: flex;
      gap: 20px;
    }

    .resume-header-icons i {
      font-size: calc(var(--base-font-size) * 1.8);
      display: inline-block !important;
      width: auto !important;
      height: auto !important;
    }

    /* Basic Info Section */
    .basic-info-section {
      padding: 20px 30px 0px 30px;
      display: grid;
      grid-template-columns: 1fr auto; /* Main content | Photo */
      gap: 25px;
      /* border-bottom: 1px solid var(--border-color); */
      align-items: start;
    }

    /* When no photo, use single column layout */
    .basic-info-section.no-photo {
      grid-template-columns: 1fr;
    }

    .basic-info-left {
      display: flex;
      flex-direction: column;
      gap: 15px;
      padding: 0px;
    }

    .basic-info-name h2 {
      display: flex;
      align-items: center;
      /* margin-bottom: 10px; */
    }

    .basic-info-name h2 span {
      font-size: calc(var(--base-font-size) *1.8);
      font-weight: bold;
      color: #000000;
    }
    .basic-info-name h2::after {
      content: "";
      flex-grow: 1;
      height: 1px;
      margin-left: 10px;
      background-color: var(--theme-color);
    }

    .basic-info-details {
      display: grid;
      grid-template-columns: repeat(2, minmax(200px, 1fr)); /* Responsive columns */
      gap: 8px 15px; /* Row and column gap */
      font-size: var(--base-font-size);
      color: var(--secondary-text-color);
      /* background-color: rgb(255, 255, 255); */
    }

    .basic-info-details p {
      display: flex;
      align-items: baseline;
    }

    .basic-info-details p span:first-child {
      font-weight: bold;
      min-width: 75px; /* Adjusted for labels like "政治面貌" */
      text-align: justify;
      color: var(--text-color);
      /* text-align: center; remove this to align left */
      background-color: rgb(255, 255, 255);
      margin-right: 5px; /* Add some space between label and value */
    }
     .basic-info-details p span:last-child {
       word-break: break-all;
     }

    .basic-info-photo {
      width: 110px;
      height: 154px;
      object-fit: cover;
      /* border: 1px solid var(--border-color); */
      align-self: center;
    }

    /* General Section Styling */
    .section {
      padding: 10px 20px;
      margin-bottom: 0px;
    }
    .section#name-header {
      padding: 15px 25px 0px 25px;
    }

    .section-header {
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      background-color: rgb(255, 255, 255);
    }

    .section-header i {
      margin-right: 10px;
      width: 18px;
      font-size: calc(var(--base-font-size) * 1.1);
      color: #fff; /* Icon color inside title */
      display: inline-block; /* 确保图标正确显示 */
    }

    .section-title {
      display: flex;
      align-items: center;
      font-size: calc(var(--base-font-size) * 1.3);
      background-color: var(--theme-color);
      border-radius: 6px;
      padding: 6px 15px;
      color: #fff;
      margin-right:10px; /* Space between title and line */
    }

    /* 确保Font Awesome图标正确显示 */
    .section-title i {
      display: inline-block !important;
      width: auto !important;
      height: auto !important;
      margin-right: 8px;
    }
    .section-title h2 {
      font-weight: bold;
      font-size: calc(var(--base-font-size) * 1.3);
      color: #fff; /* Ensure h2 color is white */
    }

    .section-header-line { /* Renamed from ::after for clarity */
      flex-grow: 1;
      height: 1px;
      background-color: var(--theme-color);
    }

    .section-content {
      padding-left: 10px;
      background-color: rgb(255, 255, 255);
    }

    .section-item {
      margin-bottom: 5px;
    }
    .item-header {
      margin-bottom: 5px;
      padding: 0px 10px;      
    }

    .section-item .three-column {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 0px 10px;
      padding: 0px 0px;
      font-weight: bold;
    }
    .section-item .two-column {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 0px 10px;
      padding: 0px 20px;
      font-weight: bold;
    }
    .item-header .date-range {
      color: var(--secondary-text-color);
      font-weight: normal;
      text-align: right; /* Align dates to the right */
    }

    .item-description p {
      color: var(--secondary-text-color);
      padding-left: 15px;
    }
    .item-description p strong {
        color: var(--text-color);
        margin-right: 5px;
    }

    .horizon-item-list {
        padding-left: 20px;
        display: flex;
        align-items: center;
        flex-wrap:wrap;
        gap: 0px 25px; /* Reduced gap */
    }
    .horizon-item-list li {
        margin-bottom: 10px; /* Add some bottom margin for wrapped items */
        background-color: #f0f0f0; /* Light background for items */
        padding: 0px 8px;
        border-radius: 4px;
        font-size: calc(var(--base-font-size) * 0.95);
    }
     .long-text-item {
        margin-bottom: 10px;
        line-height: var(--spacing);
     }
     .long-text-item .date-prefix {
         font-weight: bold;
         margin-right: 8px;
         color: var(--text-color);
     }
     .long-text-item p, .long-text-item span:not(.date-prefix) {
         color: var(--secondary-text-color);
     }

    .hidden {
      display: none;
    }

    @page {
      size: A4;
      margin: 0;
    }

    @media print {
      body {
        background-color: white;
        padding: 0;
      }
      .resume-container {
        box-shadow: none;
        margin: 0;
        width: 100%;
        min-height: 0;
      }
      .section {
        break-inside: avoid;
        page-break-inside: avoid;
      }
      .section-header {
        break-inside: avoid;
        page-break-inside: avoid;
      }
      .section-item, .long-text-item, .horizon-item-list li {
        break-inside: avoid;
        page-break-inside: avoid;
      }
      .item-description p {
        orphans: 3;
        widows: 3;
      }
    }

    p, h1, h2, h3, span, li, div {
       word-wrap: break-word;
       overflow-wrap: break-word;
       max-width: 100%;
    }

    /* 确保所有Font Awesome图标正确显示 */
    .fa, .fas, .far, .fal, .fab {
      display: inline-block !important;
      font-style: normal !important;
      font-variant: normal !important;
      text-rendering: auto !important;
      line-height: 1 !important;
    }
  </style>
</head>
<body>
  <div class="resume-container">

    <header class="resume-header">
      <span class="resume-header-title">个人简历</span>
      <div class="resume-header-icons">
        <i class="fas fa-building" aria-hidden="true"></i>
        <i class="fas fa-graduation-cap" aria-hidden="true"></i>
        <i class="fas fa-seedling" aria-hidden="true"></i>
      </div>
    </header>

    
    <div class="section" id="name-header">
      <div class="basic-info-name">
          <h2>
              <span>张展伟</span>
          </h2>
      </div>
    </div>
    

    
    <section class="basic-info-section no-photo">
      <div class="basic-info-left">
        <div class="basic-info-details">
          
          
          <p><span>电话:</span><span>1231367566</span></p>
          
          
          
          
          
          
          
          
          
          
          
          
          
        </div>
      </div>
      
    </section>
    

    
      


      

      

      

      

      

      

      

      

      

      

      

      

    
      


      
      <section class="section" id="education-section">
        <div class="section-header">
          <div class="section-title">
            <i class="fas fa-graduation-cap" aria-hidden="true"></i>
            <h2>教育背景</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          
          <div class="section-item">
            <div class="item-header three-column">
              <span>上海交大</span>
              <span>会计学 (本科)</span>
              <span class="date-range">2025-06 - 2025-06</span>
            </div>
            
            <div class="item-description">
              <p>统计学, 数学, 英语, 市场营销</p>
            </div>
            
          </div>
          
          <div class="section-item">
            <div class="item-header three-column">
              <span>上海复旦大学</span>
              <span>新闻学 (硕士)</span>
              <span class="date-range">2025-06 - 2025-06</span>
            </div>
            
            <div class="item-description">
              <p>媒体传播, 组织学, 营销学</p>
            </div>
            
          </div>
          
        </div>
      </section>
      

      

      

      

      

      

      

      

      

      

      

      

    
      


      

      

      

      

       
      <section class="section" id="school-section">
        <div class="section-header">
          <div class="section-title">
            <i class="fas fa-school" aria-hidden="true"></i>
            <h2>在校经历</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          
          <div class="section-item">
          <div class="item-header three-column"> 
            <span>学生会会长:</span>
            <span class="empty-span"></span>  
            <span class="date-range">2025-06 - 2025-06</span>
            <!-- <span class="date-prefix">2025-06</span> -->
            <!-- <span>在任职学生会会长期间, 我管理了1000人的社团, 组织外联, 喝酒聚会</span> -->
          </div>
            
            <div class="item-description">
              <p>在任职学生会会长期间, 我管理了1000人的社团, 组织外联, 喝酒聚会</p>
            </div>
            
          </div>
          
          <div class="section-item">
          <div class="item-header three-column"> 
            <span>篮球社队长:</span>
            <span class="empty-span"></span>  
            <span class="date-range">2025-06 - 2025-06</span>
            <!-- <span class="date-prefix">2025-06</span> -->
            <!-- <span>我篮球打的特别好, 在胜利拿过奖, 可以帮领导一起大球</span> -->
          </div>
            
            <div class="item-description">
              <p>我篮球打的特别好, 在胜利拿过奖, 可以帮领导一起大球</p>
            </div>
            
          </div>
          
        </div>
      </section>
      

      

      

      

      

      

      

      

    
      


      

      

      

      

      

      

      

      

      

      

      
      <section class="section" id="custom2-section">
        <div class="section-header">
            <div class="section-title">
                <i class="fas fa-star" aria-hidden="true"></i>
                <h2>名称二</h2>
            </div>
            <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          
            <div class="section-item">
               <div class="item-header two-column">
                 <span> 大幅</span>
                 <span class="date-range">2025-06 - 至今</span>
               </div>
               
                 <div class="item-description">
                    <p>习近平指出，校正中美关系这艘大船的航向，需要我们把好舵、定好向，尤其是排除各种干扰甚至破坏，这尤为重要。根据美方提议，两国经贸牵头人在日内瓦举行会谈，迈出了通过对话协商解决经贸问题的重要一步，受到两国各界和国际社会普遍欢迎，也证明对话和合作是唯一正确的选择。</p>
                 </div>
               
            </div>
          
        </div>
      </section>
      

      

    
      


      

      

      

      

      

      
      <section class="section" id="skills-section">
        <div class="section-header">
          <div class="section-title">
            <i class="fas fa-certificate" aria-hidden="true"></i>
            <h2>技能证书</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          <ul class="horizon-item-list">
            
            <li>技能1</li>
            
            <li>技能2</li>
            
            <li>技能3</li>
            
            <li>技能4</li>
            
            <li>技能5</li>
            
          </ul>
        </div>
      </section>
      

      

      

      

      

      

      

    
      


      

      

      

      

      

      

      

      

      
      <section class="section" id="evaluation-section">
        <div class="section-header">
          <div class="section-title">
            <i class="fas fa-comment" aria-hidden="true"></i>
            <h2>自我评价</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
           
          <div class="item-description">
            <p>CSS align-items 属性设置了所有直接子元素的 align-self 值作为一个组。在 Flexbox 中，它控制子元素在交叉轴上的对齐。在 Grid 布局中，它控制了子元素在其网格区域内的块向轴上的对齐。

下面的交互示例演示了使用网格布局的 align-items 的一些值。</p> 
          </div>
          
        </div>
      </section>
      

      

      

      

    
      


      

      

      

      

      

      

      

      

      

      
      <section class="section" id="custom1-section">
        <div class="section-header">
            <div class="section-title">
                <i class="fas fa-star" aria-hidden="true"></i>
                <h2>规划</h2>
            </div>
            <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          
            <div class="section-item">
               <div class="item-header two-column">
                 <span> 队长</span>
                 <span class="date-range">2025-06 - 2025-06</span>
               </div>
               
                 <div class="item-description">
                    <p>习近平指出，校正中美关系这艘大船的航向，需要我们把好舵、定好向，尤其是排除各种干扰甚至破坏，这尤为重要。根据美方提议，两国经贸牵头人在日内瓦举行会谈，迈出了通过对话协商解决经贸问题的重要一步，受到两国各界和国际社会普遍欢迎，也证明对话和合作是唯一正确的选择。</p>
                 </div>
               
            </div>
          
        </div>
      </section>
      

      

      

    
      


      

      

      
      <section class="section" id="work-section">
        <div class="section-header">
          <div class="section-title">
            <i class="fas fa-briefcase" aria-hidden="true"></i>
            <h2>工作经历</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          
          <div class="section-item">
            <div class="item-header three-column">
              <span>上海证券有限责任公司</span>
              <span>投资顾问</span>
              <span class="date-range">2025-06 - 至今</span>
            </div>
            
            <div class="item-description">
              <p>投资顾问分析, 基金分析, 上市公司调研
上市公司年报统计, 每周投资报告
组织部门活动, 高净值活动</p>
            </div>
            
          </div>
          
          <div class="section-item">
            <div class="item-header three-column">
              <span>上海宽辅私募基金合伙企业</span>
              <span>交易员职位</span>
              <span class="date-range">2025-06 - 2025-06</span>
            </div>
            
            <div class="item-description">
              <p>交易活动, 账户统计, 数据处理, 交易活动, 账户统计, 数据处理, 交易活动, 账户统计, 数据处理
每天都有很多交易任务</p>
            </div>
            
          </div>
          
        </div>
      </section>
      

      

      

      

      

      

      

      

      

      

    
      


      

      

      

      
      <section class="section" id="project-section">
        <div class="section-header">
          <div class="section-title">
            <i class="fas fa-project-diagram" aria-hidden="true"></i>
            <h2>项目经历</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          
          <div class="section-item">
            <div class="item-header three-column">
              <span>ctp数据录取</span>
              <span>程序员</span>
              <span class="date-range">2025-06 - 2025-06</span>
            </div>
            
            <div class="item-description">
              <p>期货账户编写c++程序录制实时行情, level1 高频行情, 可以为交易分析 做基础</p>
            </div>
            
          </div>
          
          <div class="section-item">
            <div class="item-header three-column">
              <span>多空融券约券系统</span>
              <span>程序员技术员</span>
              <span class="date-range">2025-06 - 2025-06</span>
            </div>
            
            <div class="item-description">
              <p>根据也无需要, 对接券商空头约券系统, 实现实时约券查券
根据也无需要, 对接券商空头约券系统, 实现实时约券查券</p>
            </div>
            
          </div>
          
        </div>
      </section>
      

      

      

      

      

      

      

      

      

    
      


      

      

      

      

      

      

      
      <section class="section" id="awards-section">
        <div class="section-header">
          <div class="section-title">
            <i class="fas fa-award" aria-hidden="true"></i>
            <h2>奖项荣誉</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          <ul class="horizon-item-list">
            
            <li>奖项1</li>
            
            <li>奖项2</li>
            
            <li>奖项3</li>
            
            <li>奖项4</li>
            
          </ul>
        </div>
      </section>
      

      

      

      

      

      

    
      
      
      <section class="section" id="jobIntention-section">
        <div class="section-header">
          <div class="section-title">
            <i class="fas fa-bullseye" aria-hidden="true"></i>
            <h2>求职意向</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
            <div class="basic-info-details">
                <p><span>期望职位:</span><span>总裁</span></p>
                <p><span>期望城市:</span><span>上海</span></p>
                <p><span>期望薪资:</span><span>3k以下</span></p>
                <p><span>求职状态:</span><span>目前在职</span></p>
            </div>
        </div>
      </section>
      


      

      

      

      

      

      

      

      

      

      

      

      

    
      


      

      
      <section class="section" id="internship-section">
        <div class="section-header">
          <div class="section-title">
            <i class="fas fa-id-badge" aria-hidden="true"></i>
            <h2>实习经历</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          
          <div class="section-item">
            <div class="item-header three-column">
              <span>阿里集团</span>
              <span>秘书</span>
              <span class="date-range">2025-06 - 2025-06</span>
            </div>
             
            <div class="item-description">
               <p>帮领导订饭店, 打酱油
参加部门会议</p>
            </div>
            
          </div>
          
          <div class="section-item">
            <div class="item-header three-column">
              <span>量化私募</span>
              <span>IT</span>
              <span class="date-range">2025-06 - 2025-06</span>
            </div>
             
            <div class="item-description">
               <p>抓数据, 洗数据, 做项目
啦啦啦</p>
            </div>
            
          </div>
          
        </div>
      </section>
      

      

      

      

      

      

      

      

      

      

      

    
      


      

      

      

      

      

      

      

      

      

      

      

      
      <section class="section" id="custom3-section">
        <div class="section-header">
            <div class="section-title">
                <i class="fas fa-star" aria-hidden="true"></i>
                <h2>人呢</h2>
            </div>
            <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          
            <div class="section-item">
               <div class="item-header two-column">
                 <span> 角色</span>
                 <span class="date-range">2025-06 - 2025-06</span>
               </div>
               
                 <div class="item-description">
                    <p>习近平指出，校正中美关系这艘大船的航向，需要我们把好舵、定好向，尤其是排除各种干扰甚至破坏，这尤为重要。根据美方提议，两国经贸牵头人在日内瓦举行会谈，迈出了通过对话协商解决经贸问题的重要一步，受到两国各界和国际社会普遍欢迎，也证明对话和合作是唯一正确的选择。</p>
                 </div>
               
            </div>
          
        </div>
      </section>
      

    
      


      

      

      

      

      

      

      

      

      

      

      

      

    
  </div>
</body>
</html>