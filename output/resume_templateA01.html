<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>张雨桐的简历</title>
<link rel="stylesheet" href="http://localhost:18080/static/fontawesome/css/all.min.css">
<style>
:root {
--theme-color: #2B6CB0;
--base-font-size: 11pt;
--font-size: var(--base-font-size);
--spacing: 1.5;
--text-color: #333333;
--secondary-color: #666666;
}

@page {
size: A4;
margin: 0;
}

body {
margin: 0;
padding: 0;
font-family: "Source Han Sans SC VF", sans-serif;
}


.resumeTemplateA01 {
width: 100%;
height: 100%;
margin: 0;
padding: 0;
display: flex;
flex-direction: column;
}

/* 添加打印媒体查询和分页控制 */
@media print {
.resumeTemplateA01 {
    overflow: hidden;
    margin: 0;
    padding: 10mm;
}

.section {
    break-inside: avoid;
    page-break-inside: avoid;
    margin-bottom: 8mm;
    position: relative;
}

.title {
    break-after: avoid;
    page-break-after: avoid;
}

.edu-item,
.work-item,
.project-item,
.internship-item,
.info-wrapper,
.custom-header,
.school-item {
    break-inside: avoid;
    page-break-inside: avoid;
    margin-bottom: 4mm;
}

.description {
    orphans: 3;
    widows: 3;
}

.photo-wrapper,
.photo,
img {
    break-inside: avoid;
    page-break-inside: avoid;
}

.content {
    break-before: avoid;
    page-break-before: avoid;
}

table, ul, ol {
    break-inside: avoid;
    page-break-inside: avoid;
}
}

.resumeTemplateA01 {
position: relative;
width: 210mm;
/* min-height: 297mm; */
margin: 0 auto;
padding: 10mm;
box-sizing: border-box;
font-size: var(--font-size);
line-height: var(--spacing);
color: var(--text-color);
page-break-after: always;
}

.resume-decoration-line {
content: '';
position: absolute;
left: 13mm;
top: calc(var(--base-font-size) + 2pt + 25mm);
bottom: 10mm;
width: 0.05mm;
background-color: var(--theme-color);
opacity: 0.8;
z-index: 1;
}

.resume-header {
margin-bottom: 5mm;
display: flex;
align-items: center;
margin-top: 2mm;
}

.icons-group {
display: flex;
gap: 3mm;
margin-left: 110mm;
margin-top: -21pt;
}

.resume-title {
font-size: 30pt;
font-weight: bold;
color: var(--theme-color);
margin-top: -25pt;
margin-bottom: 0pt;
margin-left: -2mm;
margin-right: 0pt;
}

.icon-circle {
width: 10mm;
height: 10mm;
border-radius: 50%;
background-color: var(--theme-color);
display: flex;
align-items: center;
justify-content: center;
}

.icon-circle svg {
width: 5mm;
height: 5mm;
}

.decoration-line {
width: 104%;
margin: -10mm -10mm 15mm -10mm;
padding: 0 10mm;
text-align: left;
margin-left: -23mm;
margin-top: -3mm;
}

.decoration-line svg {
width: 107%;
height: 10mm;
}

.section {
margin-bottom: 5mm;
padding: 3mm;
border-radius: 2mm;
margin-top: -10mm;
position: relative;
}

.section-basic-info {
margin-top: -18mm;
}

.basic-info-decoration {
width: 87%;
margin-top: -3.6mm;
margin-left: -12mm;
transform: scale(1.3);
transform-origin: left center;
color: var(--theme-color);
}

.basic-info-decoration svg {
width: 100%;
height: auto;
}

.title {
position: relative;
z-index: 2;
font-size: calc(var(--base-font-size) + 0pt);
line-height: 1;
color: #FFFFFF;
font-weight: bold;
padding: 2mm 0 2mm -2mm;
letter-spacing: 2pt;
}

.section-basic-info .title {
position: relative;
z-index: 2;
font-size: calc(var(--base-font-size) + 0pt);
line-height: 1;
color: #FFFFFF;
font-weight: bold;
padding: 2mm 0 2mm -2mm;
}

.content {
margin-bottom: 3mm;
margin-top: 3mm;
padding: 0 5mm;
font-size: calc(var(--base-font-size) + 0pt);
line-height: var(--spacing);
color: var(--text-color, #333333);
gap: 1mm;
display: flex;
flex-wrap: wrap;
letter-spacing: 1.2pt;
}

.degree, .time {
color: var(--secondary-color, #666666);
font-size: calc(var(--base-font-size) + 0pt);
line-height: var(--spacing);
font-weight: bold;
}

/* .courses {
color: var(--text-color, #333333);
margin-top: 0.5mm;
font-size: calc(var(--base-font-size) - 2pt);
line-height: var(--spacing);
} */

.description {
color: var(--text-color, #333333);
margin-top: 0.5mm;
white-space: pre-wrap;
word-break: break-all;
font-size: calc(var(--base-font-size) + 0pt);
line-height: var(--spacing);
width: 100%;
}

.info-item {
margin-bottom: 1.5mm;
line-height: var(--spacing);
color: var(--text-color);
}

.info-item span {
font-size: calc(var(--base-font-size));
}

.info-grid {
display: grid;
grid-template-columns: 60% 1fr;
gap: 2.5mm;
width: 100%;
}

.edu-item, .work-item, .internship-item, .project-item, .school-item {
margin-bottom: 3mm;
width: 750px;
}

.time {
color: var(--text-color);
font-size: calc(var(--base-font-size) + 0pt);
margin-bottom: 1.5mm;
font-weight: bold;
}

.school, .company {
font-weight: bold;
margin-bottom: 1.5mm;
}

.major, .position {
color: var(--secondary-color);
margin-bottom: 1.5mm;
}

.info-wrapper {
display: flex;
justify-content: space-between;
align-items: flex-start;
width: 100%;
margin-bottom: 2.5mm;
}

.info-grid {
flex: 1;
display: grid;
grid-template-columns: 1fr 1fr;
gap: 2mm;
margin-right: 5mm;
}

.photo-wrapper {
flex-shrink: 0;
width: 35mm;
height: 45mm;
border: 0.35mm solid #ddd;
display: flex;
align-items: center;
justify-content: center;
background: #fff;
}

.photo {
max-width: 100%;
max-height: 100%;
object-fit: contain;
}

.info-item {
margin-bottom: 1.5mm;
font-size: calc(var(--base-font-size) + 0pt);
line-height: var(--spacing);
color: var(--text-color);
}

.info-item .label {
display: inline-block;
min-width: 12mm;
vertical-align: top;
}

.info-item .value {
display: inline-block;
word-break: break-word;
overflow-wrap: break-word;
max-width: calc(100% - 15mm);
}

/* 求职意向样式 */
.job-intention-content {
display: flex;
flex-direction: column;
/* gap: 2mm; */
/* background-color: #ad5a5a; */
}

.job-intention-row {
/* display: flex;
gap: 3mm; */
display: grid;
grid-template-columns: 1fr 1fr;
gap: 3mm;
margin-bottom: 2mm;
/* background-color: #be2020; */
}

.job-intention-item {
flex: 1;
font-size: calc(var(--base-font-size) + 0pt);
line-height: var(--spacing);
/* line-height: 0.5; */
color: var(--text-color);
}

.job-intention-label {
font-weight: bold;
margin-right: 2mm;
}

.job-intention-value {
color: var(--text-color);
}

/* 在校经历样式 */
.school-header {
display: grid;
grid-template-columns: 1fr 1fr 1fr;
gap: 3mm;
align-items: baseline;
margin-bottom: 2mm;
width: 100%;
min-height: 6mm;
}

.school-role, .school-date {
font-weight: bold;
font-size: calc(var(--base-font-size) + 0pt);
line-height: var(--spacing);
color: var(--text-color);
display: block;
}

.school-role {
text-align: left;
}

.school-date {
text-align: left;
}

.school-content {
color: var(--text-color);
margin-top: 0.5mm;
white-space: pre-wrap;
word-break: break-all;
font-size: calc(var(--base-font-size) + 0pt);
line-height: var(--spacing);
width: 100%;
}

/* 教育经历样式 */
.education-header {
display: grid;
grid-template-columns: 1fr 1fr 1fr;
gap: 3mm;
align-items: baseline;
margin-bottom: 2mm;
width: 100%;
min-height: 6mm;
/* background-color: #be2020; */
}

.school, .major-degree, .edu-date {
font-weight: bold;
font-size: calc(var(--base-font-size) + 0pt);
line-height: var(--spacing);
color: var(--text-color);
display: block;
}

.school {
text-align: left;
}

.major-degree {
text-align: left;
}

.edu-date {
text-align: left;
}

/* .courses-label {
font-weight: bold;
} */

/* 工作经历样式 */
.work-header {
display: grid;
grid-template-columns: 1fr 1fr 1fr;
gap: 3mm;
align-items: baseline;
margin-bottom: 2mm;
width: 100%;
min-height: 6mm;
}

.company, .position, .work-date {
font-weight: bold;
font-size: calc(var(--base-font-size) + 0pt);
line-height: var(--spacing);
color: var(--text-color);
display: block;
}

.company {
text-align: left;
}

.position {
text-align: left;
}

.work-date {
text-align: left;
}

/* 实习经历样式 */
.internship-header {
display: grid;
grid-template-columns: 1fr 1fr 1fr;
gap: 3mm;
align-items: baseline;
margin-bottom: 2mm;
width: 100%;
min-height: 6mm;
}

.intern-company, .intern-position, .intern-date {
font-weight: bold;
font-size: calc(var(--base-font-size) + 0pt);
line-height: var(--spacing);
color: var(--text-color);
display: block;
}

.intern-company {
text-align: left;
}

.intern-position {
text-align: left;
}

.intern-date {
text-align: left;
}

/* 项目经历样式 */
.project-header {
display: grid;
grid-template-columns: 1fr 1fr 1fr;
gap: 3mm;
align-items: baseline;
margin-bottom: 2mm;
width: 100%;
min-height: 6mm;
}

.project-name, .project-role, .project-date {
font-weight: bold;
font-size: calc(var(--base-font-size) + 0pt);
line-height: var(--spacing);
color: var(--text-color);
display: block;
}

.project-name {
text-align: left;
}

.project-role {
text-align: left;
}

.project-date {
text-align: left;
}

/* 技能和获奖样式 */
.three-column {
display: grid;
grid-template-columns: 1fr 1fr 1fr;
gap: 3mm;
align-items: baseline;
margin-bottom: 2mm;
width: 100%;
min-height: 6mm;
}


.skill-item, .award-item, .interest-item {
margin-bottom: 1.5mm;
font-size: calc(var(--base-font-size) + 0pt);
line-height: var(--spacing);
color: var(--text-color);
}

/* 自定义模块样式 */
.custom-header {
display: grid;
grid-template-columns: 1fr 1fr 1fr;
gap: 3mm;
align-items: baseline;
margin-bottom: 2mm;
width: 100%;
min-height: 6mm;
}

.custom-name, .time {
font-weight: bold;
font-size: calc(var(--base-font-size) + 0pt);
line-height: var(--spacing);
color: var(--text-color);
display: block;
}

.custom-name {
text-align: left;
}

.time {
text-align: left;
}

/* 隐藏空模块 */
.hidden {
display: none;
}
</style>
</head>

<body>

<div class="resumeTemplateA01">
<div class="resume-decoration-line"></div>
<!-- 主标题 -->
<div class="resume-header">
    <div class="resume-title">个人简历</div>
    <div class="icons-group">
        <div class="icon-circle">
            <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="200" height="200">
                <path d="M946.2 553.66H766.9l0.35 73.56h-63.78l-0.35-73.56h-382l0.35 73.56h-63.78l-0.35-73.56H65v281.23a59.79 59.79 0 0 0 59.79 59.79h774.42A59.79 59.79 0 0 0 959 834.89V553.7q-6.4-0.03-12.8-0.04zM77.8 502.12h179.62l0.22-17.4a16 16 0 0 1 16-15.79h31.79a16 16 0 0 1 16 16.2l-0.21 17h381.83l0.22-17.4a16 16 0 0 1 16-15.79h31.78a16 16 0 0 1 16 16.2l-0.22 17h192V352.55a64 64 0 0 0-63.78-63.78H767.54V193.1a64 64 0 0 0-63.78-63.78H320.24a64 64 0 0 0-63.78 63.78v95.67H128.9a64 64 0 0 0-63.78 63.78v149.53q6.34 0.03 12.68 0.04z m243.27-308.91l0.11-0.11h381.64l0.11 0.11v95.56H321.07z" fill="#ffffff"></path>
            </svg>
        </div>
        <div class="icon-circle">
            <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="200" height="200">
                <path d="M466.090667 148.181333a86.485333 86.485333 0 0 1 78.848 0.768L951.466667 362.026667a17.28 17.28 0 0 1-0.298667 30.805333l-405.76 203.093333a86.485333 86.485333 0 0 1-78.890667-0.768L60.032 382.037333a17.28 17.28 0 0 1 0.256-30.805333l405.802667-203.050667z M211.2 502.314667v193.109333c0 6.570667 3.712 12.544 9.557333 15.488l266.154667 132.992c11.861333 5.973333 25.813333 6.101333 37.845333 0.426667l281.856-133.546667a17.28 17.28 0 0 0 9.898667-15.616v-191.786667l-310.784 155.392-294.528-156.458666z" fill="#ffffff"></path>
            </svg>
        </div>
        <div class="icon-circle">
            <svg viewBox="0 0 1028 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="200" height="200">
                <path d="M1018.319924 112.117535q4.093748 9.210934 6.652341 21.492179t2.558593 25.585928-5.117186 26.609365-16.374994 25.585928q-12.281245 12.281245-22.003898 21.492179t-16.886712 16.374994q-8.187497 8.187497-15.351557 14.32812l-191.382739-191.382739q12.281245-11.257808 29.167958-27.121083t28.144521-25.074209q14.32812-11.257808 29.679676-15.863275t30.191395-4.093748 28.656239 4.605467 24.050772 9.210934q21.492179 11.257808 47.589826 39.402329t40.425766 58.847634zM221.062416 611.554845q6.140623-6.140623 28.656239-29.167958t56.289041-56.80076l74.710909-74.710909 82.898406-82.898406 220.038979-220.038979 191.382739 192.406177-220.038979 220.038979-81.874969 82.898406q-40.937484 39.914047-73.687472 73.175753t-54.242167 54.753885-25.585928 24.562491q-10.234371 9.210934-23.539054 19.445305t-27.632802 16.374994q-14.32812 7.16406-41.960921 17.398431t-57.824197 19.957024-57.312478 16.886712-40.425766 9.210934q-27.632802 3.070311-36.843736-8.187497t-5.117186-37.867173q2.046874-14.32812 9.722653-41.449203t16.374994-56.289041 16.886712-53.730448 13.304682-33.773425q6.140623-14.32812 13.816401-26.097646t22.003898-26.097646z" fill="#ffffff"></path>
            </svg>
        </div>
    </div>
</div>
<!-- 装饰线 -->
<div class="decoration-line">
    <svg width="2000" height="25" xmlns="http://www.w3.org/2000/svg">
        <g>
            <rect stroke="#AAAAAA" id="svg_4" height="8" width="900" y="9.5" x="427" fill="#AAAAAA"/>
            <path stroke="var(--theme-color)" id="svg_5" d="m10,5l417,0l0,13l-417,0l0,-13z" fill="var(--theme-color)"/>
            <path stroke="var(--theme-color)" id="svg_6" d="m427,18l0,-13l11.4,13l-11.4,0z" fill="var(--theme-color)"/>
        </g>
    </svg>
</div>


<!-- 根据moduleOrders排序显示各个模块 -->

  <!-- 基本信息 -->
  
  <div class="section section-basic-info">
      <div class="title">基本信息</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          <div class="info-wrapper">
              <div class="info-grid">
                  <div class="info-item"><span class="label">姓名：</span><span class="value">张雨桐</span></div>
                  <div class="info-item"><span class="label">性别：</span><span class="value">女</span></div>
                  <div class="info-item"><span class="label">年龄：</span><span class="value">30</span></div>
                  <div class="info-item"><span class="label">电话：</span><span class="value">13812345673</span></div>
                  <div class="info-item"><span class="label">邮箱：</span><span class="value"><EMAIL></span></div>
                  <div class="info-item"><span class="label">城市：</span><span class="value">北京市朝阳区</span></div>
                  
                  
                  
                  
                  <div class="info-item"><span class="label">籍贯：</span><span class="value">山东省济南市</span></div>
                  <div class="info-item"><span class="label">身高：</span><span class="value">165</span></div>
                  <div class="info-item"><span class="label">体重：</span><span class="value">52kg</span></div>
                  <div class="info-item"><span class="label">微信：</span><span class="value">zhangyt_wechat</span></div>
              </div>
              
              <div class="photo-wrapper">
                  <img src="data:image/jpeg;base64,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" alt="证件照" class="photo" onerror="this.onerror=null; this.src='data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22150%22%20height%3D%22200%22%20viewBox%3D%220%200%20150%20200%22%3E%3Crect%20fill%3D%22%23f0f0f0%22%20width%3D%22150%22%20height%3D%22200%22%2F%3E%3Ctext%20fill%3D%22%23888%22%20font-family%3D%22sans-serif%22%20font-size%3D%2220%22%20dy%3D%22.3em%22%20text-anchor%3D%22middle%22%20x%3D%2275%22%20y%3D%22100%22%3E照片%3C%2Ftext%3E%3C%2Fsvg%3E'" />
              </div>
              
          </div>
      </div>
  </div>
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  
  <div class="section">
      <div class="title">求职意向</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content job-intention-content">
          <div class="job-intention-row">
              <div class="job-intention-item"><span class="job-intention-label">期望职位：</span><span class="job-intention-value">数据分析师</span></div>
              <div class="job-intention-item"><span class="job-intention-label">期望薪资：</span><span class="job-intention-value">20-30k</span></div>
          </div>
          <div class="job-intention-row">
              <div class="job-intention-item"><span class="job-intention-label">期望城市：</span><span class="job-intention-value">北京</span></div>
              <div class="job-intention-item"><span class="job-intention-label">求职状态：</span><span class="job-intention-value">在职-考虑机会</span></div>
          </div>
      </div>
  </div>
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  
  <div class="section">
      <div class="title">教育经历</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          
          <div class="edu-item">
              <div class="education-header">
                  <div class="school">北京大学</div>
                  <div class="major-degree">统计学 / 硕士</div>
                  <div class="edu-date">2018-09 - 2020-06</div>
              </div>
              <div class="description">主攻金融统计建模方向，GPA 3.8/4.0，获校级优秀毕业生</div>
          </div>
          
          <div class="edu-item">
              <div class="education-header">
                  <div class="school">山东大学</div>
                  <div class="major-degree">数学与应用数学 / 高中</div>
                  <div class="edu-date">2014-09 - 2018-06</div>
              </div>
              <div class="description">校数学建模竞赛一等奖，保研至北京大学</div>
          </div>
          
      </div>
  </div>
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  
  <div class="section">
      <div class="title">在校经历</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          
          <div class="school-item">
              <div class="school-header">
                  <div class="school-role">学生会学术部部长</div>
                  <div class="empty-div"></div>
                  <div class="school-date">2016-06 - 2017-06</div>
              </div>
              <div class="school-content">组织10+场校级学术讲座，覆盖学生2000+人次</div>
          </div>
          
      </div>
  </div>
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  
  <div class="section">
      <div class="title">实习经历</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          
          <div class="internship-item">
              <div class="internship-header">
                  <div class="intern-company">腾讯公司</div>
                  <div class="intern-position">数据分析实习生</div>
                  <div class="intern-date">2020-07 - 2020-12</div>
              </div>
              <div class="description">参与微信支付用户行为分析项目，搭建RFM用户分层模型，输出3份分析报告被业务部门采纳</div>
          </div>
          
      </div>
  </div>
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  
  <div class="section">
      <div class="title">工作经历</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          
          <div class="work-item">
              <div class="work-header">
                  <div class="company">字节跳动</div>
                  <div class="position">高级数据分析师</div>
                  <div class="work-date">2021-03 - 至今</div>
              </div>
              <div class="description">负责抖音电商用户增长分析，设计AB测试框架优化投放策略，年贡献GMV增长超1.2亿元</div>
          </div>
          
      </div>
  </div>
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  
  <div class="section">
      <div class="title">自我评价</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          
          <div class="description">具备扎实的统计学基础与业务敏感度，擅长通过数据挖掘发现增长机会点</div>
          
      </div>
  </div>
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  
  <div class="section">
      <div class="title">技能特长</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          <div class="three-column">
              
              <div class="skill-item">Python数据分析（Pandas/NumPy）</div>
              
              <div class="skill-item">SQL数据库查询</div>
              
              <div class="skill-item">Tableau数据可视化</div>
              
              <div class="skill-item">机器学习（Sklearn/TensorFlow）</div>
              
              <div class="skill-item">AB测试设计与分析</div>
              
          </div>
      </div>
  </div>
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  
  <div class="section">
      <div class="title">项目经历</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          
          <div class="project-item">
              <div class="project-header">
                  <div class="project-name">抖音电商用户留存预测模型</div>
                  <div class="project-role">项目负责人</div>
                  <div class="project-date">2022-03 - 2022-11</div>
              </div>
              <div class="description">基于XGBoost构建预测模型，AUC值达0.89，帮助业务部门降低用户流失率15%</div>
          </div>
          
      </div>
  </div>
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  


</div> <!-- 关闭 resumeTemplateA01 -->


</body>
</html>