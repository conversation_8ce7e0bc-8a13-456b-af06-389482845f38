<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>
    张展伟的简历
  </title>
  <link rel="stylesheet" href="http://0.0.0.0:18080/static/fontawesome/css/all.min.css">
  <style>
    /* 全局样式 */
    :root {
      /* 动态主题配置变量 */
      --theme-color: #44546B;
      --base-font-size: 11pt;
      --max-font-size: 13pt;
      --spacing: 1.5;
      --text-color: #000000;
      --secondary-color: #e44c4c;
    }


    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      background-color: #ffffff;
      display: flex;
      justify-content: center;
      padding: 0px;
      overflow-x: hidden; /* 防止水平滚动 */
      font-family: "Source Han Sans SC VF", sans-serif;
    }

    .resume-container {
      width: 210mm;
      /* min-height: 297mm; */
      background-color: white;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: column;
      overflow: hidden; /* 防止内容溢出 */
      font-size: var(--base-font-size);
      line-height: var(--spacing);
    }

    .resume-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: var(--theme-color);
      padding: 10px 20px;
      width: 100%;
    }

    /* SVG图标的通用样式 */
    .header-icons svg{
      filter: brightness(0) invert(1);
    }


    /* 为左侧栏的图标设置黑色 */
    .left-column-title svg.icon-1,
    .left-column-title img.icon-1 {
      width: 20px;
      height: 20px;
      margin-right: 10px;
      filter: none;
      color: black; /* 为使用currentColor的图标设置颜色 */
    }

    /* 图标尺寸 */
    .header-icons .icon-1 {
      width: 26px;
      height: 26px;
      color: white;
      vertical-align: middle;
      margin-right: 10px;
    }

    .header-icons .icon-2 {
      width: 20px;
      height: 20px;
      vertical-align: middle;
      color: white;
      margin-right: 10px;
    }

    .header-icons .icon-3 {
      width: 14px;
      height: 14px;
      vertical-align: middle;
      color: white;
      margin-right: 10px;
    }

    .header-icons .icon-4 {
      width: 8px;
      height: 8px;
      vertical-align: middle;
      color: white;
      margin-right: 10px;
    }

    .left-column .icon-1 {
      width: 26px;
      height: 26px;
      margin-right: 10px;
    }

    .content {
      display: grid;
      grid-template-columns: 2.5fr 7.5fr; /* 左右分栏设为3:7比例 */
      width: 100%;
      min-height: 0; /* 修复IE中的grid内容溢出问题 */
    }

    /* 左侧栏样式 */
    .left-column {
      background-color: #f0f0f0;
      padding: 0 10px 20px;
      overflow: hidden; /* 防止内容溢出 */
      box-sizing: border-box; /* 确保padding不会增加宽度 */
      height: 100%;
      /* min-height: calc(297mm - 45px); A4页面高度减去标题高度 */
      position: relative; /* 添加相对定位 */
    }

    /* 更新左侧栏图标样式 */
    .left-column .icon-1 {
      width: 26px;
      height: 26px;
      margin-right: 10px;
    }

    .photo-container {
      padding: 15px;
      margin: 15px;
      display: flex;
      justify-content: center;
    }

    .photo-container img {
      width: 130px; /* 缩小照片尺寸 */
      height: 180px;
      object-fit: cover;
      border: 1px solid #f0f0f0;
    }

    .name-container {
      text-align: center;
      padding: 10px 0;
      margin: 15px;
    }

    .name-container h1 {
      font-size: calc(var(--base-font-size) * 1.8); /* 调整字体大小 */
      font-weight: bold;
      word-break: break-word; /* 允许长名字换行 */
    }

    /* 右侧栏样式 */
    .right-column {
      padding: 15px;
      overflow: hidden; /* 防止内容溢出 */
      box-sizing: border-box; /* 确保padding不会增加宽度 */
    }

    /* 通用部分样式 */
    .section {
      margin-bottom: 10px;
      overflow: hidden; /* 防止内容溢出 */
    }

    .section-tile-and-line {
      display: grid;
      grid-template-columns: auto 1fr; /* 标题自适应宽度，线条占据剩余空间 */
      align-items: center;
      background-color: #ffffff;
      margin-bottom: 10px;
      gap: 10px; /* 设置标题和线条之间的间距 */
    }

    .section-title {
      display: flex;
      align-items: center;
      padding: 8px 15px;
      background-color: var(--theme-color);
      color: white;
      border-radius: 4px;
      width: auto; /* 自动宽度 */
    }

    .section-title i {
      margin-right: 10px;
      font-size: calc(var(--base-font-size) * 1.2);
    }

    .section-title h2 {
      font-size: calc(var(--base-font-size) * 1.2);
      font-weight: normal;
      white-space: nowrap; /* 防止文字换行 */
    }

    .section-title-line {
      height: 1px;
      background-color: var(--theme-color);
      margin-right: 10px;
    }

    .section-content {
      padding: 0 0px;
      color: black;
      word-wrap: break-word; /* 允许长词换行 */
      overflow-wrap: break-word; /* 现代浏览器支持 */
    }

    .left-column .section-content {
      font-size: min(var(--base-font-size), var(--max-font-size));
      line-height: 1.0;
    }

    .left-column #contact-section-content {
      font-size: min(var(--base-font-size), var(--max-font-size));
      line-height: 1.0;
    }

    .left-column .section-content p {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 20px; /* 添加行间距 */
    }

    .left-column .section-content p span:first-child {
      width: 75px;
      font-weight: bold;
      white-space: nowrap; /* 防止字段名称换行 */
      flex-shrink: 0; /* 防止字段名称被压缩 */
    }

    #contact-section-content p span:first-child {
      width: auto;
      font-weight: bold;
      white-space: nowrap; /* 防止字段名称换行 */
      flex-shrink: 0; /* 防止字段名称被压缩 */
    }

    .left-column-strip {
      width: 100%;
      height: 18px;
      background-color: var(--theme-color);
      border-radius: 4px;
    }

    .left-column-title {
      width: 100%;
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 8px 15px;
      margin-bottom: 10px;
    }

    /* 替换i元素样式为img元素样式 */
    .left-column-title i {
      margin-right: 10px;
      font-size: calc(var(--base-font-size) * 1.2);
    }

    .left-column-title h2 {
      font-size: calc(var(--base-font-size) * 1.2);
      font-weight: bold;
      color: black;
    }

    /* 教育背景样式 */
    .three-column-header {
      display: grid;
      grid-template-columns: 0.8fr 1fr 0.8fr;
      justify-content: space-between;
      flex-wrap: wrap;
      gap: 5px; /* 添加列间距 */
      margin-bottom: 10px;
      font-weight: bold;
    }

    .education-item .experience-item .internship-item {
      margin-bottom: 10px;
    }

    .education-courses {
      line-height: var(--spacing);
      margin-bottom: 10px;
    }

    /* 在校经历样式 */
    /* .experience-item {
      margin-bottom: 5px;
      position: relative;
    } */

    /* .experience-item:before {
      content: "";
      position: absolute;
      left: 0;
      top: 5px;
      width: 8px;
      height: 8px;
      background-color: var(--theme-color);
      border-radius: 50%;
    } */

    /* 奖项荣誉样式 兴趣爱好样式 技能证书样式 */
    /* .award-item, .skill-item, .interest-item {
        padding-left: 20px;
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        flex-wrap:wrap;
     } */
    .award-item div, .skill-item div, .interest-item div {
      /* margin-right: 25px; */
      font-weight: normal;
    }


    /* 自我评价样式 */
    .evaluation-item {
      margin-bottom: 10px;
      position: relative;
      padding-left: 20px;
    }

    .evaluation-item:before {
      content: "";
      position: absolute;
      left: 0;
      top: 5px;
      width: 8px;
      height: 8px;
      background-color: var(--theme-color);
      border-radius: 50%;
    }

    /* 隐藏空模块 */
    .hidden {
      display: none;
    }

    /* 确保内容超长时可以自动换行 */
    p, h1, h2, h3, span {
      max-width: 100%;
      overflow-wrap: break-word;
      word-wrap: break-word;
    }

    /* 列表样式 */
    .item-list {
      margin: 0;
      padding: 0;
      list-style: none;
    }

    .item-list li {
      margin-bottom: 10px;
      position: relative;
      padding-left: 20px;
    }

    .item-list li:before {
      content: "";
      position: absolute;
      left: 0;
      top: 5px;
      width: 8px;
      height: 8px;
      background-color: var(--theme-color);
      border-radius: 50%;
    }

    /* 打印媒体查询 */
    @page {
      size: A4;
      margin: 0;
      /* padding: 3mm 3mm; */
    }

    @media print {
      .resume-container {
        box-shadow: none;
        margin: 0;
        overflow: hidden;
      }

      .section-tile-and-line, .section{
        break-inside: avoid;
        page-break-inside: avoid;
      }
      /* .section-content {
        orphans: 3;
        widows: 3;
      } */
    }
  </style>
</head>

<body>
  <div class="resume-container">
    <!-- 简历头部 -->
    <header class="resume-title">
      <div class="header-icons">
        <!-- 使用SVG use标签复用图标并控制大小 -->
        <img class="icon-1" src="http://0.0.0.0:18080/static/resume-images/templateA02/chiLun.png" alt="图标" onerror="this.style.display='none';" />
        <img class="icon-2" src="http://0.0.0.0:18080/static/resume-images/templateA02/chiLun.png" alt="图标" onerror="this.style.display='none';" />
        <img class="icon-3" src="http://0.0.0.0:18080/static/resume-images/templateA02/chiLun.png" alt="图标" onerror="this.style.display='none';" />
        <img class="icon-4" src="http://0.0.0.0:18080/static/resume-images/templateA02/chiLun.png" alt="图标" onerror="this.style.display='none';" />
      </div>
      <div class="header-image-right">
        <img src="http://0.0.0.0:18080/static/resume-images/templateA02/personal_resume.png" alt="图标" onerror="this.style.display='none';" />
      </div>
    </header>

    <div class="content">
      <!-- 左侧栏 -->
      <div class="left-column">
        <!-- 照片区域 -->
        

        <!-- 姓名 -->
        <div class="name-container">
          <h1 id="name">张展伟</h1>
        </div>

        <!-- 联系方式 -->
        <div class="section " id="contact-section">
          <div class="left-column-strip"></div>
          <div class="left-column-title">
            <!-- 使用SVG use标签复用联系方式图标 -->
            <!-- <img class="icon-1" src="http://0.0.0.0:18080/static/resume-images/templateA02/tel.svg" alt="联系方式图标" onerror="this.style.display='none';" /> -->
            <i class="fas fa-mobile-screen"></i>

            <h2>联系方式</h2>
          </div>
          <div class="section-content" id="contact-section-content">
            <p><span>电话：</span><span id="phone">1231367566</span></p>
            
            
          </div>
        </div>

        <!-- 个人信息 -->
        <div class="section " id="personal-info-section">
          <div class="left-column-strip"></div>
          <div class="left-column-title">
            <!-- 使用SVG use标签复用个人信息图标 -->
            <!-- <img class="icon-1" src="http://0.0.0.0:18080/static/resume-images/templateA02/info.svg" alt="个人信息图标" onerror="this.style.display='none';" /> -->
            <i class="fas fa-id-card"></i>
            <h2>个人信息</h2>
          </div>
          <div class="section-content">
            
            
            
            
            
            
            
            
            
            
            
          </div>
        </div>
      </div>


      <!-- 右侧栏 -->
      <div class="right-column">
        
          <!-- 求职意向 -->
          


          <!-- 教育背景 -->
          

          <!-- 实习经历 -->
          

          <!-- 在校经历 -->
          

          <!-- 工作经历 -->
          

          <!-- 项目经历 -->
          

          <!-- 技能证书 -->
          

          <!-- 奖项荣誉 -->
          

          <!-- 兴趣爱好 -->
          

          <!-- 自我评价 -->
          

          <!-- 自定义模块1 -->
          

          <!-- 自定义模块2 -->
          

          <!-- 自定义模块3 -->
          
        
          <!-- 求职意向 -->
          


          <!-- 教育背景 -->
          
          <div class="section" id="education-section">
            <div class="section-tile-and-line">
              <div class="section-title">
                <i class="fas fa-graduation-cap"></i>
                <h2>教育背景</h2>
              </div>
              <div class="section-title-line"></div>
            </div>
            <div class="section-content" id="education-content">
              
                
                  <div class="education-item">
                    <div class="three-column-header">
                      <span>2025-06-2025-06</span>
                      <span>上海交大</span>
                      <span>会计学（本科）</span>
                    </div>
                    <div class="education-courses">
                      <p><span>主修课程：</span><span>统计学, 数学, 英语, 市场营销</span></p>
                    </div>
                  </div>
                
                  <div class="education-item">
                    <div class="three-column-header">
                      <span>2025-06-2025-06</span>
                      <span>上海复旦大学</span>
                      <span>新闻学（硕士）</span>
                    </div>
                    <div class="education-courses">
                      <p><span>主修课程：</span><span>媒体传播, 组织学, 营销学</span></p>
                    </div>
                  </div>
                
              
            </div>
          </div>
          

          <!-- 实习经历 -->
          

          <!-- 在校经历 -->
          

          <!-- 工作经历 -->
          

          <!-- 项目经历 -->
          

          <!-- 技能证书 -->
          

          <!-- 奖项荣誉 -->
          

          <!-- 兴趣爱好 -->
          

          <!-- 自我评价 -->
          

          <!-- 自定义模块1 -->
          

          <!-- 自定义模块2 -->
          

          <!-- 自定义模块3 -->
          
        
          <!-- 求职意向 -->
          


          <!-- 教育背景 -->
          

          <!-- 实习经历 -->
          

          <!-- 在校经历 -->
          
          <div class="section" id="school-experience-section">
            <div class="section-tile-and-line">
              <div class="section-title">
                <i class="fas fa-school"></i>
                <h2>在校经历</h2>
              </div>
              <div class="section-title-line"></div>
            </div>
            <div class="section-content" id="school-experience-content">
              
                
                  <div class="education-item">
                    <div class="three-column-header">
                      <span>2025-06-2025-06</span>
                      <span class="empty-span"></span>  
                      <span>学生会会长</span>
                    </div>
                    <div class="education-courses">
                      <p><span>在任职学生会会长期间, 我管理了1000人的社团, 组织外联, 喝酒聚会</span></p>
                    </div>
                  </div>
                
                  <div class="education-item">
                    <div class="three-column-header">
                      <span>2025-06-2025-06</span>
                      <span class="empty-span"></span>  
                      <span>篮球社队长</span>
                    </div>
                    <div class="education-courses">
                      <p><span>我篮球打的特别好, 在胜利拿过奖, 可以帮领导一起大球</span></p>
                    </div>
                  </div>
                
              

            </div>
          </div>
          

          <!-- 工作经历 -->
          

          <!-- 项目经历 -->
          

          <!-- 技能证书 -->
          

          <!-- 奖项荣誉 -->
          

          <!-- 兴趣爱好 -->
          

          <!-- 自我评价 -->
          

          <!-- 自定义模块1 -->
          

          <!-- 自定义模块2 -->
          

          <!-- 自定义模块3 -->
          
        
          <!-- 求职意向 -->
          


          <!-- 教育背景 -->
          

          <!-- 实习经历 -->
          

          <!-- 在校经历 -->
          

          <!-- 工作经历 -->
          

          <!-- 项目经历 -->
          

          <!-- 技能证书 -->
          

          <!-- 奖项荣誉 -->
          

          <!-- 兴趣爱好 -->
          

          <!-- 自我评价 -->
          

          <!-- 自定义模块1 -->
          

          <!-- 自定义模块2 -->
          
          <div class="section " id="custom2-section">
            <div class="section-tile-and-line">
              <div class="section-title">
                <i class="fas fa-star"></i>
                <h2>名称二</h2>
              </div>
              <div class="section-title-line"></div>
            </div>
            <div class="section-content">
              
                
                  <div class="custom-item experience-item">
                    <div class="three-column-header">
                      <p>2025-06 - 至今</p>
                      <p class="empty_p"></p>
                      <p>大幅</p>
                    </div>
                    <div class="education-courses">
                      <p>习近平指出，校正中美关系这艘大船的航向，需要我们把好舵、定好向，尤其是排除各种干扰甚至破坏，这尤为重要。根据美方提议，两国经贸牵头人在日内瓦举行会谈，迈出了通过对话协商解决经贸问题的重要一步，受到两国各界和国际社会普遍欢迎，也证明对话和合作是唯一正确的选择。</p>
                    </div>
                  </div>
                
              
            </div>
          </div>
          

          <!-- 自定义模块3 -->
          
        
          <!-- 求职意向 -->
          


          <!-- 教育背景 -->
          

          <!-- 实习经历 -->
          

          <!-- 在校经历 -->
          

          <!-- 工作经历 -->
          

          <!-- 项目经历 -->
          

          <!-- 技能证书 -->
          
          <div class="section" id="skills-section">
            <div class="section-tile-and-line">
              <div class="section-title">
                <i class="fas fa-certificate"></i>
                <h2>技能证书</h2>
              </div>
              <div class="section-title-line"></div>
            </div>
            <div class="section-content" id="skills-content">
              <div class="skill-item three-column-header">
                
                  <div>技能1</div>
                
                  <div>技能2</div>
                
                  <div>技能3</div>
                
                  <div>技能4</div>
                
                  <div>技能5</div>
                
              </div>
            </div>
          </div>
          

          <!-- 奖项荣誉 -->
          

          <!-- 兴趣爱好 -->
          

          <!-- 自我评价 -->
          

          <!-- 自定义模块1 -->
          

          <!-- 自定义模块2 -->
          

          <!-- 自定义模块3 -->
          
        
          <!-- 求职意向 -->
          


          <!-- 教育背景 -->
          

          <!-- 实习经历 -->
          

          <!-- 在校经历 -->
          

          <!-- 工作经历 -->
          

          <!-- 项目经历 -->
          

          <!-- 技能证书 -->
          

          <!-- 奖项荣誉 -->
          

          <!-- 兴趣爱好 -->
          

          <!-- 自我评价 -->
          
          <div class="section" id="self-evaluation-section">
            <div class="section-tile-and-line">
              <div class="section-title">
                <i class="fas fa-comment"></i>
                <h2>自我评价</h2>
              </div>
              <div class="section-title-line"></div>
            </div>
            <div class="section-content" id="self-evaluation-content">
               
              <div class="long-text-item">
                <p>CSS align-items 属性设置了所有直接子元素的 align-self 值作为一个组。在 Flexbox 中，它控制子元素在交叉轴上的对齐。在 Grid 布局中，它控制了子元素在其网格区域内的块向轴上的对齐。

下面的交互示例演示了使用网格布局的 align-items 的一些值。</p> 
              </div>
              



            </div>
          </div>
          

          <!-- 自定义模块1 -->
          

          <!-- 自定义模块2 -->
          

          <!-- 自定义模块3 -->
          
        
          <!-- 求职意向 -->
          


          <!-- 教育背景 -->
          

          <!-- 实习经历 -->
          

          <!-- 在校经历 -->
          

          <!-- 工作经历 -->
          

          <!-- 项目经历 -->
          

          <!-- 技能证书 -->
          

          <!-- 奖项荣誉 -->
          

          <!-- 兴趣爱好 -->
          

          <!-- 自我评价 -->
          

          <!-- 自定义模块1 -->
          

          <div class="section" id="custom1-section">
            <div class="section-tile-and-line">
              <div class="section-title">
                <i class="fas fa-star"></i>
                <h2>规划</h2>
              </div>
              <div class="section-title-line"></div>
            </div>
            <div class="section-content">
              
                
                  <div class="custom-item experience-item">
                    <div class="three-column-header">
                      <p>2025-06 - 2025-06</p>
                      <p class="empty_p"></p>
                      <p>队长</p>
                    </div>
                    <div class="education-courses">
                      <p>习近平指出，校正中美关系这艘大船的航向，需要我们把好舵、定好向，尤其是排除各种干扰甚至破坏，这尤为重要。根据美方提议，两国经贸牵头人在日内瓦举行会谈，迈出了通过对话协商解决经贸问题的重要一步，受到两国各界和国际社会普遍欢迎，也证明对话和合作是唯一正确的选择。</p>
                    </div>
                  </div>
                
              
            </div>
          </div>
          

          <!-- 自定义模块2 -->
          

          <!-- 自定义模块3 -->
          
        
          <!-- 求职意向 -->
          


          <!-- 教育背景 -->
          

          <!-- 实习经历 -->
          

          <!-- 在校经历 -->
          

          <!-- 工作经历 -->
          
          <div class="section" id="work-section">
            <div class="section-tile-and-line">
              <div class="section-title">
                <i class="fas fa-briefcase"></i>
                <h2>工作经历</h2>
              </div>
              <div class="section-title-line"></div>
            </div>
            <div class="section-content" id="work-content">
              
                
                  <div class="education-item">
                    <div class="three-column-header">
                      <span>2025-06-至今</span>
                      <span>上海证券有限责任公司</span>  
                      <span>投资顾问</span>
                    </div>
                    <div class="education-courses">
                      <p><span>投资顾问分析, 基金分析, 上市公司调研
上市公司年报统计, 每周投资报告
组织部门活动, 高净值活动</span></p>
                    </div>
                  </div>
                
                  <div class="education-item">
                    <div class="three-column-header">
                      <span>2025-06-2025-06</span>
                      <span>上海宽辅私募基金合伙企业</span>  
                      <span>交易员职位</span>
                    </div>
                    <div class="education-courses">
                      <p><span>交易活动, 账户统计, 数据处理, 交易活动, 账户统计, 数据处理, 交易活动, 账户统计, 数据处理
每天都有很多交易任务</span></p>
                    </div>
                  </div>
                
              
            </div>
          </div>
          

          <!-- 项目经历 -->
          

          <!-- 技能证书 -->
          

          <!-- 奖项荣誉 -->
          

          <!-- 兴趣爱好 -->
          

          <!-- 自我评价 -->
          

          <!-- 自定义模块1 -->
          

          <!-- 自定义模块2 -->
          

          <!-- 自定义模块3 -->
          
        
          <!-- 求职意向 -->
          


          <!-- 教育背景 -->
          

          <!-- 实习经历 -->
          

          <!-- 在校经历 -->
          

          <!-- 工作经历 -->
          

          <!-- 项目经历 -->
          
          <div class="section" id="project-section">
            <div class="section-tile-and-line">
              <div class="section-title">
                <i class="fas fa-project-diagram"></i>
                <h2>项目经历</h2>
              </div>
              <div class="section-title-line"></div>
            </div>
            <div class="section-content" id="project-content">
              
                
                  <div class="project-item">
                    <div class="three-column-header">
                      <span>2025-06-2025-06</span>
                      <span>ctp数据录取</span>  
                      <span>程序员</span>
                    </div>
                    <div class="education-courses">
                      <p><span>期货账户编写c++程序录制实时行情, level1 高频行情, 可以为交易分析 做基础</span></p>
                    </div>
                  </div>
                
                  <div class="project-item">
                    <div class="three-column-header">
                      <span>2025-06-2025-06</span>
                      <span>多空融券约券系统</span>  
                      <span>程序员技术员</span>
                    </div>
                    <div class="education-courses">
                      <p><span>根据也无需要, 对接券商空头约券系统, 实现实时约券查券
根据也无需要, 对接券商空头约券系统, 实现实时约券查券</span></p>
                    </div>
                  </div>
                
              
            </div>
          </div>
          

          <!-- 技能证书 -->
          

          <!-- 奖项荣誉 -->
          

          <!-- 兴趣爱好 -->
          

          <!-- 自我评价 -->
          

          <!-- 自定义模块1 -->
          

          <!-- 自定义模块2 -->
          

          <!-- 自定义模块3 -->
          
        
          <!-- 求职意向 -->
          


          <!-- 教育背景 -->
          

          <!-- 实习经历 -->
          

          <!-- 在校经历 -->
          

          <!-- 工作经历 -->
          

          <!-- 项目经历 -->
          

          <!-- 技能证书 -->
          

          <!-- 奖项荣誉 -->
          
          <div class="Section" id="awards-section">
            <div class="section-tile-and-line">
              <div class="section-title">
                <i class="fas fa-award"></i>
                <h2>奖项荣誉</h2>
              </div>
              <div class="section-title-line"></div>
            </div>
            <div class="section-content" id="awards-content">
              <div class="award-item three-column-header">
                
                  <div>奖项1</div>
                
                  <div>奖项2</div>
                
                  <div>奖项3</div>
                
                  <div>奖项4</div>
                
              </div>
            </div>
          </div>
          

          <!-- 兴趣爱好 -->
          

          <!-- 自我评价 -->
          

          <!-- 自定义模块1 -->
          

          <!-- 自定义模块2 -->
          

          <!-- 自定义模块3 -->
          
        
          <!-- 求职意向 -->
          
          <div class="section" id="job-intention-section">
            <div class="section-tile-and-line">
              <div class="section-title">
                <i class="fas fa-bullseye"></i>
                <h2>求职意向</h2>
              </div>
              <div class="section-title-line"></div>
            </div>
            <!-- <div class="section-content" style="display: flex; gap:20px; flex-wrap:wrap;"> -->
            <div class="section-content" style="display: grid; grid-template-columns: repeat(2, 1fr); gap:20px; flex-wrap:wrap;">
              <p><span>期望职位：</span><span id="position">总裁</span></p>
              <p><span>期望薪资：</span><span id="salary">3k以下</span></p>
              <p><span>期望城市：</span><span id="job-city">上海</span></p>
              <p><span>求职状态：</span><span id="job-status">目前在职</span></p>
            </div>
          </div>
          


          <!-- 教育背景 -->
          

          <!-- 实习经历 -->
          

          <!-- 在校经历 -->
          

          <!-- 工作经历 -->
          

          <!-- 项目经历 -->
          

          <!-- 技能证书 -->
          

          <!-- 奖项荣誉 -->
          

          <!-- 兴趣爱好 -->
          

          <!-- 自我评价 -->
          

          <!-- 自定义模块1 -->
          

          <!-- 自定义模块2 -->
          

          <!-- 自定义模块3 -->
          
        
          <!-- 求职意向 -->
          


          <!-- 教育背景 -->
          

          <!-- 实习经历 -->
          
          <div class="section" id="internship-section">
            <div class="section-tile-and-line">
              <div class="section-title">
                <i class="fas fa-id-badge"></i>
                <h2>实习经历</h2>
              </div>
              <div class="section-title-line"></div>
            </div>
            <div class="section-content" id="internship-content">
              
                
                  <div class="internship-item">
                    <div class="three-column-header">
                      <span>2025-06-2025-06</span>
                      <span>阿里集团</span>
                      <span>秘书</span>
                    </div>
                    <div class="education-courses">
                      <p><span>帮领导订饭店, 打酱油
参加部门会议</span></p>
                    </div>
                  </div>
                
                  <div class="internship-item">
                    <div class="three-column-header">
                      <span>2025-06-2025-06</span>
                      <span>量化私募</span>
                      <span>IT</span>
                    </div>
                    <div class="education-courses">
                      <p><span>抓数据, 洗数据, 做项目
啦啦啦</span></p>
                    </div>
                  </div>
                
              
            </div>
          </div>
          

          <!-- 在校经历 -->
          

          <!-- 工作经历 -->
          

          <!-- 项目经历 -->
          

          <!-- 技能证书 -->
          

          <!-- 奖项荣誉 -->
          

          <!-- 兴趣爱好 -->
          

          <!-- 自我评价 -->
          

          <!-- 自定义模块1 -->
          

          <!-- 自定义模块2 -->
          

          <!-- 自定义模块3 -->
          
        
          <!-- 求职意向 -->
          


          <!-- 教育背景 -->
          

          <!-- 实习经历 -->
          

          <!-- 在校经历 -->
          

          <!-- 工作经历 -->
          

          <!-- 项目经历 -->
          

          <!-- 技能证书 -->
          

          <!-- 奖项荣誉 -->
          

          <!-- 兴趣爱好 -->
          

          <!-- 自我评价 -->
          

          <!-- 自定义模块1 -->
          

          <!-- 自定义模块2 -->
          

          <!-- 自定义模块3 -->
          
          <div class="section " id="custom3-section">
            <div class="section-tile-and-line">
              <div class="section-title">
                <i class="fas fa-star"></i>
                <h2>人呢</h2>
              </div>
              <div class="section-title-line"></div>
            </div>
            <div class="section-content">
              
                
                  <div class="custom-item experience-item">
                    <div class="three-column-header">
                      <p>2025-06 - 2025-06</p>
                      <p class="empty_p"></p>
                      <p>角色</p>
                    </div>
                    <div class="education-courses">
                      <p>习近平指出，校正中美关系这艘大船的航向，需要我们把好舵、定好向，尤其是排除各种干扰甚至破坏，这尤为重要。根据美方提议，两国经贸牵头人在日内瓦举行会谈，迈出了通过对话协商解决经贸问题的重要一步，受到两国各界和国际社会普遍欢迎，也证明对话和合作是唯一正确的选择。</p>
                    </div>
                  </div>
                
              
            </div>
          </div>
          
        
          <!-- 求职意向 -->
          


          <!-- 教育背景 -->
          

          <!-- 实习经历 -->
          

          <!-- 在校经历 -->
          

          <!-- 工作经历 -->
          

          <!-- 项目经历 -->
          

          <!-- 技能证书 -->
          

          <!-- 奖项荣誉 -->
          

          <!-- 兴趣爱好 -->
          

          <!-- 自我评价 -->
          

          <!-- 自定义模块1 -->
          

          <!-- 自定义模块2 -->
          

          <!-- 自定义模块3 -->
          
        
      </div>
    </div>
  </div>
</body>
</html>