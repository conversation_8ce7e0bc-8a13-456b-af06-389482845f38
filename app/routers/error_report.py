"""
错误上报系统API路由
"""
from fastapi import APIRouter, HTTPException, Depends, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, desc
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import logging

from app.database import get_db
from app.models import User, ErrorReport
from app.schemas.user import (
    ErrorReportCreate,
    ErrorReportBatch,
    ErrorReportInfo,
    ErrorStatsResponse,
    MessageResponse,
    ListResponse,
    ErrorTypeEnum
)
from app.auth import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/resume/error-report",
    tags=["错误上报"],
    responses={404: {"description": "未找到"}},
)

@router.post("", response_model=MessageResponse)
async def report_error(
    error_data: ErrorReportCreate,
    db: Session = Depends(get_db)
):
    """
    单个错误上报接口
    不需要认证，避免认证失败导致无法上报错误
    """
    try:
        # 尝试根据openid查找用户ID
        user_id = None
        if error_data.openid:
            user = db.query(User).filter(User.openid == error_data.openid).first()
            if user:
                user_id = user.id
        
        # 创建错误记录
        error_report = ErrorReport(
            user_id=user_id,
            openid=error_data.openid,
            error_type=error_data.error_type.value,
            error_message=error_data.error_message,
            error_stack=error_data.error_stack,
            page_path=error_data.page_path,
            user_agent=error_data.user_agent,
            device_info=error_data.device_info,
            app_version=error_data.app_version,
            system_info=error_data.system_info,
            network_type=error_data.network_type,
            timestamp=error_data.timestamp
        )
        
        db.add(error_report)
        db.commit()
        db.refresh(error_report)
        
        logger.info(f"错误上报成功: {error_report.id}, 类型: {error_data.error_type.value}")
        
        return MessageResponse(message="错误上报成功")
        
    except Exception as e:
        logger.exception("错误上报异常")
        db.rollback()
        # 错误上报失败不应该抛出异常，避免影响用户体验
        return MessageResponse(message="错误上报失败，但不影响正常使用")

@router.post("/batch", response_model=MessageResponse)
async def report_error_batch(
    batch_data: ErrorReportBatch,
    db: Session = Depends(get_db)
):
    """
    批量错误上报接口
    不需要认证，避免认证失败导致无法上报错误
    """
    try:
        error_reports = []
        
        for error_data in batch_data.errors:
            # 尝试根据openid查找用户ID
            user_id = None
            if error_data.openid:
                user = db.query(User).filter(User.openid == error_data.openid).first()
                if user:
                    user_id = user.id
            
            # 创建错误记录
            error_report = ErrorReport(
                user_id=user_id,
                openid=error_data.openid,
                error_type=error_data.error_type.value,
                error_message=error_data.error_message,
                error_stack=error_data.error_stack,
                page_path=error_data.page_path,
                user_agent=error_data.user_agent,
                device_info=error_data.device_info,
                app_version=error_data.app_version,
                system_info=error_data.system_info,
                network_type=error_data.network_type,
                timestamp=error_data.timestamp
            )
            error_reports.append(error_report)
        
        # 批量插入
        db.add_all(error_reports)
        db.commit()
        
        logger.info(f"批量错误上报成功: {len(error_reports)} 条")
        
        return MessageResponse(message=f"批量错误上报成功，共 {len(error_reports)} 条")
        
    except Exception as e:
        logger.exception("批量错误上报异常")
        db.rollback()
        # 错误上报失败不应该抛出异常，避免影响用户体验
        return MessageResponse(message="批量错误上报失败，但不影响正常使用")

@router.get("/stats", response_model=ErrorStatsResponse)
async def get_error_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    days: int = Query(7, ge=1, le=30, description="统计天数"),
    limit: int = Query(10, ge=1, le=50, description="最近错误数量限制")
):
    """
    获取错误统计信息（需要认证，用于调试）
    """
    try:
        # 计算时间范围
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)
        
        # 查询时间范围内的错误
        query = db.query(ErrorReport).filter(
            ErrorReport.created_at >= start_time,
            ErrorReport.created_at <= end_time
        )
        
        # 总错误数
        total_errors = query.count()
        
        # 按错误类型统计
        error_type_stats = db.query(
            ErrorReport.error_type,
            func.count(ErrorReport.id).label('count')
        ).filter(
            ErrorReport.created_at >= start_time,
            ErrorReport.created_at <= end_time
        ).group_by(ErrorReport.error_type).all()
        
        error_types = {stat.error_type: stat.count for stat in error_type_stats}
        
        # 最近错误列表
        recent_errors = query.order_by(desc(ErrorReport.created_at)).limit(limit).all()
        recent_error_list = [ErrorReportInfo.model_validate(error) for error in recent_errors]
        
        return ErrorStatsResponse(
            total_errors=total_errors,
            error_types=error_types,
            recent_errors=recent_error_list,
            time_range={
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "days": days
            }
        )
        
    except Exception as e:
        logger.exception("获取错误统计异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取错误统计失败"
        )

@router.get("", response_model=ListResponse)
async def get_error_reports(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    error_type: Optional[ErrorTypeEnum] = Query(None, description="错误类型筛选"),
    days: int = Query(7, ge=1, le=30, description="查询天数"),
    limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量")
):
    """
    获取错误报告列表（需要认证，用于调试）
    """
    try:
        # 计算时间范围
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)
        
        # 构建查询
        query = db.query(ErrorReport).filter(
            ErrorReport.created_at >= start_time,
            ErrorReport.created_at <= end_time
        )
        
        # 添加错误类型筛选
        if error_type:
            query = query.filter(ErrorReport.error_type == error_type.value)
        
        # 获取总数
        total = query.count()
        
        # 分页查询
        error_reports = query.order_by(desc(ErrorReport.created_at)).offset(offset).limit(limit).all()
        
        # 转换为响应模型
        error_list = [ErrorReportInfo.model_validate(error) for error in error_reports]
        
        return ListResponse(
            total=total,
            items=error_list
        )
        
    except Exception as e:
        logger.exception("获取错误报告列表异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取错误报告列表失败"
        )
