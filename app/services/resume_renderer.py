import os
import base64
import logging
from jinja2 import Environment, FileSystemLoader, select_autoescape
from app.schemas.resume import ResumeData
from config.fastapi_config import settings

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# RESUME_SERVER_PORT = os.getenv("RESUME_SERVER_PORT")

class ResumeRenderer:
    def __init__(self):
        # 获取模板目录的绝对路径
        template_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "app", "templates")
        self.env = Environment(
            loader=FileSystemLoader(template_dir),
            autoescape=select_autoescape(['html', 'xml'])
        )
        # 定义模块的元数据，包括 key、默认标题和图标（如果模板需要）
        # 注意：这里的 key 必须与 ResumeData 中的字段名以及 moduleOrders 中的键名一致
        self.module_meta = [
            {'key': 'basicInfo', 'title': '基本信息', 'icon': 'fas fa-user'},
            {'key': 'jobIntention', 'title': '求职意向', 'icon': 'fas fa-bullseye'},
            {'key': 'education', 'title': '教育背景', 'icon': 'fas fa-graduation-cap'},
            {'key': 'school', 'title': '在校经历', 'icon': 'fas fa-school'},
            {'key': 'internship', 'title': '实习经历', 'icon': 'fas fa-id-badge'},
            {'key': 'work', 'title': '工作经历', 'icon': 'fas fa-briefcase'},
            {'key': 'project', 'title': '项目经历', 'icon': 'fas fa-project-diagram'},
            {'key': 'skills', 'title': '技能证书', 'icon': 'fas fa-certificate'},
            {'key': 'awards', 'title': '奖项荣誉', 'icon': 'fas fa-award'},
            {'key': 'interests', 'title': '兴趣爱好', 'icon': 'fas fa-heart'},
            {'key': 'evaluation', 'title': '自我评价', 'icon': 'fas fa-comment'},
            # 自定义模块的 key 需要与 ResumeData 中的字段完全一致
            {'key': 'custom1', 'title': '自定义模块1', 'icon': 'fas fa-star'},
            {'key': 'custom2', 'title': '自定义模块2', 'icon': 'fas fa-star'},
            {'key': 'custom3', 'title': '自定义模块3', 'icon': 'fas fa-star'},
        ]

    # def process_photo(self, resume_data: ResumeData):
    #     """处理照片数据，如果存在Base64编码的照片"""
    #     if resume_data.basicInfo.photoUrl and resume_data.basicInfo.photoUrl.startswith("data:image"):
    #         # 提取实际的base64数据
    #         try:
    #             _, data = resume_data.basicInfo.photoUrl.split(',', 1)
    #             # 在这里我们只是保留处理后的base64数据用于模板渲染
    #             resume_data.basicInfo.photoUrl = data
    #         except Exception as e:
    #             print(f"处理照片数据时出错: {e}")
    #     return resume_data

    def render_template(self,
                        resume_data: ResumeData,
                        template_id_from_client: str = "templateA02.html",
                        config_from_client: dict = None) -> str:
        """渲染简历模板

        Args:
            resume_data: 简历数据对象
            template_name: 模板文件名
            theme_config: 主题配置，如颜色、字体大小等
        """
        logger.info(f"=== 开始渲染简历模板 ===")
        logger.info(f"模板ID: {template_id_from_client}")
        logger.info(f"主题配置: {config_from_client}")

        print(f'TEMPLATE_ID: {template_id_from_client}')
        print(f'CONFIG: {config_from_client}')
        # print(f'resume_data: {resume_data}')

        # 处理照片
        # resume_data = self.process_photo(resume_data)

        # 获取模板
        if not template_id_from_client.endswith('.html'):
            template_id_from_client = f'{template_id_from_client}.html'

        logger.info(f"最终模板文件名: {template_id_from_client}")

        try:
            template = self.env.get_template(template_id_from_client)
            logger.info(f"成功加载模板: {template.name}")
        except Exception as e:
            logger.error(f"加载模板失败: {e}")
            raise

        print(f'template file : {template}')


        # TODO 修改默认主题配置
        # 默认主题配置
        default_theme = {
            "theme_color": "#44546b",
            "base_font_size": 11,
            "max_font_size": 13,
            "spacing": 1.5,
        }

        # 合并配置
        if 'themeColor' in config_from_client:
            default_theme['theme_color'] = config_from_client['themeColor']
            logger.info(f"应用主题颜色: {config_from_client['themeColor']}")
        if 'fontSize' in config_from_client:
            default_theme['base_font_size'] = config_from_client['fontSize']
            logger.info(f"应用字体大小: {config_from_client['fontSize']}")
        if 'spacing' in config_from_client:
            default_theme['spacing'] = config_from_client['spacing']
            logger.info(f"应用行间距: {config_from_client['spacing']}")

        logger.info(f"最终主题配置: {default_theme}")

        # 设置隐藏标志
        hide_flags = {
            "hide_basic_info": not (resume_data.basicInfo.name or resume_data.basicInfo.phone or
                                   resume_data.basicInfo.email or resume_data.basicInfo.gender),
            "hide_job_intention": not (hasattr(resume_data, 'jobIntention') and resume_data.jobIntention),
            "hide_education": not (hasattr(resume_data, 'education') and resume_data.education and len(resume_data.education) > 0),
            "hide_school_experience": not (hasattr(resume_data, 'school') and resume_data.school and len(resume_data.school) > 0),
            "hide_work": not (hasattr(resume_data, 'work') and resume_data.work and len(resume_data.work) > 0),
            "hide_project": not (hasattr(resume_data, 'project') and resume_data.project and len(resume_data.project) > 0),
            "hide_skills": not (hasattr(resume_data, 'skills') and resume_data.skills and len(resume_data.skills) > 0),
            "hide_awards": not (hasattr(resume_data, 'awards') and resume_data.awards and len(resume_data.awards) > 0),
            "hide_interests": not (hasattr(resume_data, 'interests') and resume_data.interests and len(resume_data.interests) > 0),
            "hide_evaluation": not (hasattr(resume_data, 'evaluation') and resume_data.evaluation),
            "hide_custom1": not (hasattr(resume_data, 'custom') and resume_data.custom and
                                hasattr(resume_data.custom, 'custom1') and resume_data.custom.custom1),
            "hide_custom2": not (hasattr(resume_data, 'custom') and resume_data.custom and
                                hasattr(resume_data.custom, 'custom2') and resume_data.custom.custom2),
            "hide_custom3": not (hasattr(resume_data, 'custom') and resume_data.custom and
                                hasattr(resume_data.custom, 'custom3') and resume_data.custom.custom3)
        }

        # --- 排序逻辑 ---
        # 准备包含数据和元数据的模块列表
        modules_to_sort = []
        for meta in self.module_meta:
            key = meta['key']
            data = getattr(resume_data, key, None) # 获取对应的数据
            # 只有在数据实际存在时才加入待排序列表
            if data is not None:
                 modules_to_sort.append({
                    'key': key,
                    'title': meta['title'],
                    'icon': meta['icon'],
                    'data': data,
                    'order': resume_data.moduleOrders.get(key, 99) # 获取排序值，默认为 99
                })

        # 根据 order 字段排序
        ordered_modules = sorted(modules_to_sort, key=lambda x: x['order'])
        # --- 排序逻辑结束 ---

        render_config = {
            "base_url": f"http://{settings.HOST}:{settings.PORT}"
        }

        # print(f'hide_flags: {hide_flags}')
        # print(f'hasattr(resume_data, "work"): {hasattr(resume_data, "work")}')
        # print(f'resume_data.work: {resume_data.work}')
        # print(f'len(resume_data.work): {len(resume_data.work)}')
        # 渲染模板
        render_context = {
            "resume": resume_data,
            "ordered_modules": ordered_modules,
            "hide_flags": hide_flags,
            **default_theme,
            **render_config
        }

        logger.info("开始渲染模板...")
        html_content = template.render(**render_context)
        logger.info(f"模板渲染完成，生成HTML长度: {len(html_content)}")
        logger.info("=== 简历模板渲染结束 ===")

        return html_content