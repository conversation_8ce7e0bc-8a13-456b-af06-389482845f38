const chromium = require('@sparticuz/chromium');
const puppeteer = require('puppeteer-core');
const path = require('path');
const fs = require('fs');

// 浏览器实例池
class BrowserPool {
  constructor(options = {}) {
    this.maxPoolSize = options.maxPoolSize || 3; // 最大浏览器实例数
    this.minPoolSize = options.minPoolSize || 1; // 最小浏览器实例数（保持活跃）
    this.maxPagesPerBrowser = options.maxPagesPerBrowser || 3; // 每个浏览器最多创建3个页面
    this.idleTimeout = options.idleTimeout || 180000; // 180秒浏览器空闲超时
    this.pageIdleTimeout = options.pageIdleTimeout || 90000; // 90秒页面空闲超时
    this.pool = []; // 存储浏览器实例
    this.pagePool = []; // 存储空闲页面实例
    this.launching = {}; // 正在启动的浏览器实例
    this.browserOptions = options.browserOptions || {
      protocolTimeout: 60000, // 设置协议超时为60秒
      timeout: 30000 // 设置操作超时为30秒
    }; // 浏览器启动选项
    
    // 注册定期检查，确保保持最小实例数
    this.maintenanceInterval = setInterval(() => this.ensureMinInstances(), 30000);
    
    // 启动时预热浏览器实例
    if (options.preloadOnStart !== false) {
      this.preloadInstances();
    }
  }

  // 预热浏览器实例
  async preloadInstances() {
    console.log(`BrowserPool: 预热浏览器实例，目标数量: ${this.minPoolSize}`);
    try {
      const preloadPromises = [];
      for (let i = 0; i < this.minPoolSize; i++) {
        preloadPromises.push(this.createBrowserAndAddToPool());
      }
      await Promise.all(preloadPromises);
      
      // 为每个浏览器预创建一个页面
      for (const browser of this.pool) {
        await this.createPageAndAddToPool(browser);
      }
      
      console.log(`BrowserPool: 完成预热，当前池大小: ${this.pool.length}，页面池大小：${this.pagePool.length}`);
    } catch (error) {
      console.error('BrowserPool: 预热浏览器实例失败', error);
    }
  }

  // 确保池中至少有最小数量的实例
  async ensureMinInstances() {
    const activeCount = this.pool.length + Object.keys(this.launching).length;
    if (activeCount < this.minPoolSize) {
      console.log(`BrowserPool: 检测到实例数量低于最小值，当前: ${activeCount}，最小: ${this.minPoolSize}`);
      try {
        for (let i = activeCount; i < this.minPoolSize; i++) {
          const browser = await this.createBrowserAndAddToPool();
          // 为新创建的浏览器实例预创建一个页面
          await this.createPageAndAddToPool(browser);
        }
      } catch (error) {
        console.error('BrowserPool: 确保最小实例数时出错', error);
      }
    }
  }

  // 创建浏览器并直接添加到池中
  async createBrowserAndAddToPool() {
    const browser = await this.createBrowser();
    this.pool.push(browser);
    return browser;
  }
  
  // 创建页面并添加到页面池
  async createPageAndAddToPool(browser) {
    try {
      // 确认浏览器实例仍然有效
      if (!browser || !browser.isConnected?.()) {
        console.error(`BrowserPool: 浏览器实例无效或已断开连接，无法创建页面`);
        return null;
      }
      
      // 计算该浏览器已创建的页面数量
      const pagesInBrowser = this.pagePool.filter(page => page._browserId === browser._id).length;
      
      if (pagesInBrowser < this.maxPagesPerBrowser) {
        console.log(`BrowserPool: 为浏览器 ${browser._id} 创建新页面`);
        
        // 在较长的超时时间内创建页面
        let page = null;
        try {
          page = await Promise.race([
            browser.newPage(),
            new Promise((_, reject) => setTimeout(() => reject(new Error('创建页面超时')), 30000))
          ]);
        } catch (err) {
          console.error(`BrowserPool: 创建页面超时或失败`, err);
          return null;
        }
        
        if (!page) {
          console.error(`BrowserPool: 无法为浏览器 ${browser._id} 创建页面`);
          return null;
        }
        
        // 设置页面视口
        try {
          await Promise.race([
            page.setViewport({
              width: 794,
              height: 1123,
              deviceScaleFactor: 2,
            }),
            new Promise((_, reject) => setTimeout(() => reject(new Error('设置视口超时')), 5000))
          ]);
        } catch (err) {
          console.error('BrowserPool: 设置页面视口失败', err);
          try {
            await page.close().catch(e => {});
          } catch (e) {}
          return null;
        }
        
        try {
          // 设置请求拦截
          await Promise.race([
            page.setRequestInterception(true),
            new Promise((_, reject) => setTimeout(() => reject(new Error('设置请求拦截超时')), 5000))
          ]);
        } catch (err) {
          console.error('BrowserPool: 设置请求拦截失败', err);
          // 继续执行，不中断流程
        }
        
        // 设置事件监听器
        try {
          page.on('pageerror', error => console.error('Page error:', error));
        } catch (e) {}
        
        try {
          page.on('response', response => {
            if (response.status() >= 400) {
              console.error(`请求失败: ${response.url()} ${response.status()}`);
            }
          });
        } catch (e) {}
        
        try {
          page.on('request', (request) => {
            // console.log(`加载资源: ${request.resourceType()}: ${request.url()}`);
            try {
              request.continue();
            } catch (err) {
              console.error('BrowserPool: 请求继续失败', err);
            }
          });
        } catch (e) {}
        
        try {
          page.on('requestfailed', (request) => {
            try {
              console.error(`资源加载失败: ${request.url()}, 错误: ${request.failure()?.errorText}`);
            } catch (err) {
              console.error('BrowserPool: 获取请求失败信息出错', err);
            }
          });
        } catch (e) {}
        
        // 为页面添加浏览器ID和页面ID
        page._browserId = browser._id;
        page._id = `page_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
        page._creationTime = Date.now();
        page._reuseCount = 0;
        page._hasIssues = false;
        
        // 验证页面是否可用
        try {
          const isWorking = await Promise.race([
            page.evaluate(() => true),
            new Promise((_, reject) => setTimeout(() => reject(new Error('页面验证超时')), 5000))
          ]);
          
          if (!isWorking) {
            throw new Error('页面验证失败');
          }
        } catch (err) {
          console.error(`BrowserPool: 新页面验证失败`, err);
          try {
            await page.close().catch(e => {});
          } catch (e) {}
          return null;
        }
        
        // 添加到页面池
        this.pagePool.push(page);
        console.log(`BrowserPool: 创建新页面，ID: ${page._id}，添加到页面池，当前页面池大小: ${this.pagePool.length}`);
        return page;
      } else {
        console.log(`BrowserPool: 浏览器 ${browser._id} 已达到最大页面数 ${this.maxPagesPerBrowser}`);
        return null;
      }
    } catch (error) {
      console.error('BrowserPool: 创建页面失败', error);
      return null;
    }
  }

  // 获取一个可用的页面
  async getPage() {
    console.log(`BrowserPool: 请求页面，当前页面池大小: ${this.pagePool.length}`);
    
    // 如果有可用页面，先尝试重用
    if (this.pagePool.length > 0) {
      let page = this.pagePool.shift();
      
      // 清除原有的空闲计时器
      if (page._idleTimer) {
        clearTimeout(page._idleTimer);
        page._idleTimer = null;
      }
      
      try {
        // 1. 检查页面是否已关闭或浏览器已断开
        const isClosed = page.isClosed?.();
        const isConnected = page.browser?.().isConnected?.();
        
        if (isClosed || !isConnected) {
          console.log(`BrowserPool: 页面 ${page._id} 已关闭或浏览器已断开连接，创建新页面`);
          return this.createPageForAnyBrowser();
        }
        
        // 2. 检查页面是否已被标记为问题页面
        if (page._hasIssues) {
          console.log(`BrowserPool: 页面 ${page._id} 被标记为问题页面，关闭并创建新页面`);
          try {
            await page.close().catch(e => console.error('关闭问题页面失败:', e));
          } catch (e) {}
          return this.createPageForAnyBrowser();
        }
        
        // 3. 标记重用时间以进行统计
        page._lastReuseTime = Date.now();
        if (!page._reuseCount) page._reuseCount = 0;
        page._reuseCount++;
        
        // 4. 重置请求拦截（如果存在），避免冲突
        try {
          await Promise.race([
            page.setRequestInterception(false),
            new Promise((_, reject) => setTimeout(() => reject(new Error('重置请求拦截超时')), 3000))
          ]).catch(() => console.log("重置请求拦截失败，继续执行"));
        } catch (e) {
          console.warn(`重置请求拦截失败: ${e.message}`);
        }
        
        // 5. 尝试访问空白页以重置页面状态
        console.log(`BrowserPool: 重置页面 ${page._id} 状态`);
        try {
          // 先导航到空白页以彻底清除上一个页面的所有状态和资源
          await Promise.race([
            page.goto('about:blank', { waitUntil: 'networkidle0', timeout: 5000 }),
            new Promise((_, reject) => setTimeout(() => reject(new Error('导航到空白页超时')), 5000))
          ]).catch(e => {
            console.warn(`导航到空白页失败: ${e.message}，尝试使用evaluate清除内容`);
            throw e; // 继续抛出以进入备用清理
          });
        } catch (error) {
          // 导航失败时，尝试使用evaluate清除内容
          try {
            // 6. 使用更可靠的方式清除页面内容
            await Promise.race([
              page.evaluate(() => {
                // 移除所有事件监听器
                const oldBody = document.body;
                const oldHead = document.head;
                if (oldBody) {
                  const newBody = document.createElement('body');
                  document.documentElement.replaceChild(newBody, oldBody);
                }
                if (oldHead) {
                  const newHead = document.createElement('head');
                  document.documentElement.replaceChild(newHead, oldHead);
                }
                // 清除所有定时器
                const highestTimeoutId = setTimeout(() => {}, 0);
                for (let i = 0; i < highestTimeoutId; i++) {
                  clearTimeout(i);
                  clearInterval(i);
                }
                return true;
              }),
              new Promise((_, reject) => setTimeout(() => reject(new Error('清除页面内容超时')), 3000))
            ]);
          } catch (evalError) {
            // 如果evaluate也失败，标记页面有问题，并创建新页面
            console.error(`BrowserPool: 清除页面内容失败: ${evalError.message}，创建新页面`);
            try {
              await page.close().catch(e => {});
            } catch (e) {}
            return this.createPageForAnyBrowser();
          }
        }
        
        // 7. 检验页面是否可用（通过执行简单JS）
        try {
          const checkResult = await Promise.race([
            page.evaluate(() => "page_is_working"),
            new Promise((_, reject) => setTimeout(() => reject(new Error('页面响应检查超时')), 2000))
          ]);
          
          if (checkResult !== "page_is_working") {
            throw new Error('页面状态验证失败');
          }
        } catch (validationError) {
          console.error(`BrowserPool: 页面验证失败: ${validationError.message}，创建新页面`);
          try {
            await page.close().catch(e => {});
          } catch (e) {}
          return this.createPageForAnyBrowser();
        }
        
        console.log(`BrowserPool: 成功重用页面 ${page._id}（第${page._reuseCount}次重用）`);
        return page;
        
      } catch (error) {
        console.error(`BrowserPool: 检查或重置页面状态失败: ${error.message}`);
        // 出现任何错误都不重用此页面，创建新页面
        try {
          await page.close().catch(e => console.error('关闭失败页面出错:', e));
        } catch (e) {}
        return this.createPageForAnyBrowser();
      }
    }
    
    // 如果没有可用页面，为任意浏览器创建新页面
    return this.createPageForAnyBrowser();
  }
  
  // 为任意可用浏览器创建新页面
  async createPageForAnyBrowser() {
    // 查找页面数少于最大值的浏览器
    for (const browser of this.pool) {
      const pagesInBrowser = this.pagePool.filter(page => page._browserId === browser._id).length;
      
      if (pagesInBrowser < this.maxPagesPerBrowser) {
        const page = await this.createPageAndAddToPool(browser);
        if (page) {
          // 从页面池中取出刚创建的页面
          this.pagePool = this.pagePool.filter(p => p._id !== page._id);
          return page;
        }
      }
    }
    
    // 如果所有浏览器页面都达到最大值，且未达到最大浏览器实例数，创建新浏览器
    if (this.pool.length + Object.keys(this.launching).length < this.maxPoolSize) {
      const browser = await this.createBrowser();
      this.pool.push(browser);
      const page = await this.createPageAndAddToPool(browser);
      if (page) {
        // 从页面池中取出刚创建的页面
        this.pagePool = this.pagePool.filter(p => p._id !== page._id);
        return page;
      }
    }
    
    // 如果达到最大浏览器实例数且所有浏览器页面都满了，等待页面释放
    console.log('BrowserPool: 所有浏览器页面已满，等待页面释放');
    return new Promise((resolve) => {
      const checkInterval = setInterval(() => {
        if (this.pagePool.length > 0) {
          clearInterval(checkInterval);
          resolve(this.getPage());
        }
      }, 100);
    });
  }

  // 获取一个可用的浏览器实例
  async getBrowser() {
    console.log(`BrowserPool: 请求浏览器实例，当前池大小: ${this.pool.length}/${this.maxPoolSize}`);
    
    // 检查池中是否有可用实例
    if (this.pool.length > 0) {
      const browser = this.pool.shift();
      
      // 清除原有的空闲计时器
      if (browser._idleTimer) {
        clearTimeout(browser._idleTimer);
        browser._idleTimer = null;
      }
      
      // 检查浏览器实例是否已断开连接
      try {
        if (!browser.isConnected()) {
          console.log('BrowserPool: 发现断开连接的浏览器实例，创建新实例');
          return this.createBrowser();
        }
      } catch (error) {
        console.error('BrowserPool: 检查浏览器连接时出错', error);
        return this.createBrowser();
      }
      
      console.log('BrowserPool: 返回现有浏览器实例');
      return browser;
    }
    
    // 没有可用实例且未达到最大池大小，创建新实例
    if (this.pool.length + Object.keys(this.launching).length < this.maxPoolSize) {
      return this.createBrowser();
    }
    
    // 达到最大池大小，等待实例释放
    console.log('BrowserPool: 达到最大池大小，等待可用实例');
    return new Promise((resolve) => {
      const checkInterval = setInterval(() => {
        if (this.pool.length > 0) {
          clearInterval(checkInterval);
          resolve(this.getBrowser());
        }
      }, 100);
    });
  }

  // 创建新的浏览器实例
  async createBrowser() {
    const id = Date.now().toString();
    console.log(`BrowserPool: 创建新的浏览器实例 ID: ${id}`);
    
    // 标记为正在启动
    this.launching[id] = true;
    
    let browser;
    try {
      // 创建浏览器实例
      browser = await puppeteer.launch({
        args: [
          ...chromium.args,
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--single-process',
          '--disable-gpu'
        ],
        executablePath: await chromium.executablePath(),
        headless: true,
        defaultViewport: {
          width: 794,
          height: 1123,
          deviceScaleFactor: 2,
        },
        protocolTimeout: this.browserOptions.protocolTimeout || 60000, // 确保协议超时被设置
        timeout: this.browserOptions.timeout || 30000, // 确保操作超时被设置
        ...this.browserOptions
      });

      // 监听浏览器关闭事件
      browser.on('disconnected', () => {
        console.log(`BrowserPool: 浏览器实例断开连接 ID: ${id}`);
        // 从池中移除断开连接的实例
        this.pool = this.pool.filter(b => b !== browser);
        
        // 从页面池中移除该浏览器的所有页面
        this.pagePool = this.pagePool.filter(page => page._browserId !== id);
        
        // 确保维持最小实例数
        this.ensureMinInstances();
      });

      // 为浏览器实例添加ID和创建时间
      browser._id = id;
      browser._creationTime = Date.now();
    } catch (error) {
      console.error(`BrowserPool: 创建浏览器实例失败 ID: ${id}`, error);
      delete this.launching[id];
      throw error;
    }
    
    // 移除启动标记
    delete this.launching[id];
    
    return browser;
  }

  // 释放页面回页面池
  async releasePage(page) {
    if (!page) {
      console.log('BrowserPool: 尝试释放未定义的页面，忽略');
      return;
    }
    
    // 检查页面是否已关闭或者浏览器已断开
    let shouldClosePage = false;
    try {
      if (page.isClosed?.()) {
        console.log(`BrowserPool: 尝试释放已关闭的页面，忽略`);
        return;
      }
      
      // 检查浏览器是否已断开
      if (!page.browser?.().isConnected?.()) {
        console.log(`BrowserPool: 页面的浏览器已断开连接，关闭页面`);
        shouldClosePage = true;
        return;
      }
    } catch (error) {
      console.error(`BrowserPool: 检查页面状态时出错: ${error.message}`);
      shouldClosePage = true;
      return;
    } finally {
      if (shouldClosePage) {
        try {
          await page.close().catch(e => {});
        } catch (e) {}
        return;
      }
    }
    
    // 记录使用时间，便于统计分析
    const usageDuration = page._lastReuseTime ? (Date.now() - page._lastReuseTime) : 0;
    console.log(`BrowserPool: 页面 ${page._id} 使用时长: ${usageDuration}ms`);
    
    let cleanupSuccessful = false;
    let healthCheckSuccessful = false;
    
    try {
      // 1. 先尝试导航到空白页以彻底清除状态
      try {
        await Promise.race([
          page.goto('about:blank', { waitUntil: 'networkidle0', timeout: 5000 }),
          new Promise((_, reject) => setTimeout(() => reject(new Error('导航到空白页超时')), 5000))
        ]);
        cleanupSuccessful = true;
      } catch (navError) {
        console.warn(`BrowserPool: 导航到空白页失败: ${navError.message}，尝试备用清理方法`);
        
        // 2. 如果导航失败，尝试使用evaluate清除内容
        try {
          await Promise.race([
            page.evaluate(() => {
              // 移除所有事件监听器
              const oldBody = document.body;
              const oldHead = document.head;
              if (oldBody) {
                const newBody = document.createElement('body');
                document.documentElement.replaceChild(newBody, oldBody);
              }
              if (oldHead) {
                const newHead = document.createElement('head');
                document.documentElement.replaceChild(newHead, oldHead);
              }
              // 清除所有定时器
              const highestTimeoutId = setTimeout(() => {}, 0);
              for (let i = 0; i < highestTimeoutId; i++) {
                clearTimeout(i);
                clearInterval(i);
              }
              return true;
            }),
            new Promise((_, reject) => setTimeout(() => reject(new Error('清除页面内容超时')), 3000))
          ]);
          cleanupSuccessful = true;
        } catch (evalError) {
          console.error(`BrowserPool: 备用清理也失败: ${evalError.message}`);
          cleanupSuccessful = false;
        }
      }
      
      // 3. 验证页面是否仍然可用
      if (cleanupSuccessful) {
        try {
          const result = await Promise.race([
            page.evaluate(() => "health_check_passed"),
            new Promise((_, reject) => setTimeout(() => reject(new Error('健康检查超时')), 2000))
          ]);
          
          healthCheckSuccessful = (result === "health_check_passed");
        } catch (healthError) {
          console.error(`BrowserPool: 页面健康检查失败: ${healthError.message}`);
          healthCheckSuccessful = false;
        }
      }
      
      // 4. 根据清理和健康检查结果决定是否重用页面
      if (cleanupSuccessful && healthCheckSuccessful) {
        // 如果无错误 - 重置计数器并标记为健康
        page._hasIssues = false;
        if (!page._successfulReleases) page._successfulReleases = 0;
        page._successfulReleases++;
        
        // 将页面放回池中
        this.pagePool.push(page);
        console.log(`BrowserPool: 页面 ${page._id} 成功释放回池中，当前页面池大小: ${this.pagePool.length}`);
        
        // 设置页面空闲超时 - 页面池大小超过一定阈值时考虑关闭
        if (this.pagePool.length > this.pool.length * 2) { // 页面数超过浏览器数的2倍
          page._idleTimer = setTimeout(async () => {
            if (this.pagePool.includes(page) && this.pagePool.length > this.pool.length) {
              this.pagePool = this.pagePool.filter(p => p !== page);
              console.log(`BrowserPool: 关闭空闲页面 ${page._id}，当前页面池大小: ${this.pagePool.length}`);
              
              try {
                await page.close();
              } catch (error) {
                console.error(`BrowserPool: 关闭空闲页面时出错: ${error.message}`);
              }
            }
          }, this.pageIdleTimeout);
        }
      } else {
        // 清理或健康检查失败，关闭页面
        console.log(`BrowserPool: 页面 ${page._id} 清理或健康检查失败，关闭页面而不重用`);
        try {
          await page.close();
        } catch (closeError) {
          console.error(`BrowserPool: 关闭页面失败: ${closeError.message}`);
        }
      }
    } catch (error) {
      console.error(`BrowserPool: 释放页面时出现未预期错误: ${error.message}`);
      // 出现任何其他错误，尝试关闭页面
      try {
        page._hasIssues = true;
        await page.close();
      } catch (e) {
        console.error(`BrowserPool: 关闭页面失败: ${e.message}`);
      }
    }
  }

  // 释放浏览器实例回池中
  async releaseBrowser(browser) {
    // 防止浏览器已关闭的情况
    try {
      if (!browser || !browser.isConnected()) {
        console.log('BrowserPool: 尝试释放已关闭的浏览器实例，忽略');
        // 确保维持最小实例数
        this.ensureMinInstances();
        return;
      }
    } catch (error) {
      console.error('BrowserPool: 检查浏览器连接状态时出错', error);
      // 确保维持最小实例数
      this.ensureMinInstances();
      return;
    }
    
    // 清理浏览器中的所有页面
    try {
      const pages = await browser.pages();
      await Promise.all(pages.map(page => {
        try {
          return page.close();
        } catch (e) {
          console.error('BrowserPool: 关闭页面失败', e);
          return Promise.resolve();
        }
      }));
    } catch (error) {
      console.error('BrowserPool: 清理页面时出错', error);
      
      // 如果清理页面失败，尝试关闭浏览器
      try {
        await browser.close();
      } catch (e) {
        console.error('BrowserPool: 关闭故障浏览器时出错', e);
      }
      
      // 确保维持最小实例数
      this.ensureMinInstances();
      return;
    }
    
    // 将浏览器放回池中
    this.pool.push(browser);
    console.log(`BrowserPool: 浏览器实例释放回池中，当前池大小: ${this.pool.length}/${this.maxPoolSize}`);
    
    // 设置空闲超时 - 只有当池中的实例数大于最小值时才设置超时关闭
    if (this.pool.length > this.minPoolSize) {
      browser._idleTimer = setTimeout(async () => {
        // 如果浏览器仍在池中且超时，且池大小仍然超过最小值，关闭它
        if (this.pool.includes(browser) && this.pool.length > this.minPoolSize) {
          this.pool = this.pool.filter(b => b !== browser);
          
          // 从页面池中移除该浏览器的所有页面
          this.pagePool = this.pagePool.filter(page => page._browserId !== browser._id);
          
          console.log(`BrowserPool: 关闭空闲浏览器实例，当前池大小: ${this.pool.length}/${this.maxPoolSize}`);
          
          try {
            await browser.close();
          } catch (error) {
            console.error('BrowserPool: 关闭空闲浏览器时出错', error);
          }
        }
      }, this.idleTimeout);
    } else {
      console.log(`BrowserPool: 保持浏览器实例活跃，当前池大小(${this.pool.length})不超过最小值(${this.minPoolSize})`);
    }
  }

  // 关闭所有浏览器实例
  async closeAll() {
    // 先清除维护定时器
    if (this.maintenanceInterval) {
      clearInterval(this.maintenanceInterval);
      this.maintenanceInterval = null;
    }
    
    // 清除所有页面的计时器
    for (const page of this.pagePool) {
      if (page._idleTimer) {
        clearTimeout(page._idleTimer);
        page._idleTimer = null;
      }
    }
    
    // 清空页面池
    this.pagePool = [];
    
    console.log(`BrowserPool: 关闭所有浏览器实例，当前数量: ${this.pool.length}`);
    
    // 关闭所有浏览器实例
    const closePromises = this.pool.map(async (browser) => {
      try {
        if (browser._idleTimer) {
          clearTimeout(browser._idleTimer);
          browser._idleTimer = null;
        }
        
        await browser.close();
      } catch (error) {
        console.error('BrowserPool: 关闭浏览器时出错', error);
      }
    });
    
    await Promise.all(closePromises);
    this.pool = [];
  }
}

// 创建浏览器池实例
const browserPool = new BrowserPool({
  maxPoolSize: 3, // 最大3个并发浏览器实例
  minPoolSize: 1, // 最小1个浏览器实例保持活跃
  maxPagesPerBrowser: 3, // 每个浏览器最多创建3个页面
  idleTimeout: 180000, // 180秒浏览器空闲超时
  pageIdleTimeout: 90000, // 90秒页面空闲超时
  preloadOnStart: true, // 启动时预热
  browserOptions: {
    protocolTimeout: 60000, // 设置协议超时为60秒
    timeout: 30000 // 设置操作超时为30秒
  }
});

const MAX_RETRIES = 3; // 最大重试次数
const RETRY_DELAY = 1000; // 重试延迟（毫秒）

/**
 * 处理HTML内容的基础函数，用于generatePDF和generateJPEG共享的逻辑
 * @param {string} html - 要转换的HTML内容
 * @param {Object} options - 转换选项
 * @param {string} operationType - 操作类型 'pdf' 或 'jpeg'
 * @returns {Promise<{page: Object, performance: Object}>} - 返回处理后的页面和性能数据
 */
async function processHTML(html, options = {}, operationType = 'pdf') {
  let page = null;
  
  // 性能监控
  const performance = {
    start: Date.now(),
    getPage: 0,
    setContent: 0,
    imageLoad: 0,
    total: 0
  };
  
  try {
    console.log(`process${operationType.toUpperCase()} 开始执行...`);
    console.log('收到HTML内容长度:', html?.length);

    console.log('准备获取页面...');
    const getPageStart = Date.now();
    page = await browserPool.getPage();
    performance.getPage = Date.now() - getPageStart;
    console.log(`获取页面成功，页面ID: ${page._id}，耗时: ${performance.getPage}ms`);

    // 确认页面状态良好
    try {
      await Promise.race([
        page.evaluate(() => true),
        new Promise((_, reject) => setTimeout(() => reject(new Error('页面状态检查超时')), 3000))
      ]).catch(e => {
        console.error(`页面状态检查失败: ${e.message}，尝试继续`);
      });
    } catch (err) {
      console.warn(`页面状态不佳，尝试继续: ${err.message}`);
    }

    // 设置内容 - 添加超时保护
    console.log('设置页面内容...');
    const setContentStart = Date.now();
    
    try {
      // 1. 设置请求拦截，优化加载流程
      let interceptIsSet = false; // 标记请求拦截是否已设置
      try {
        // 首先检查当前请求拦截状态
        const isIntercepting = await page.evaluate(() => {
          return window._puppeteer_request_interception_enabled || false;
        }).catch(() => false);
        
        if (!isIntercepting) {
          console.log("初始化请求拦截");
          await Promise.race([
            page.setRequestInterception(true),
            new Promise((_, reject) => setTimeout(() => reject(new Error('设置请求拦截超时')), 5000))
          ]);
          interceptIsSet = true;
          
          // 使用添加监听器而非once，避免重复问题
          const onRequest = request => {
            try {
              // 只允许文档、字体和图片通过，拦截其他不必要的请求
              const resourceType = request.resourceType();
              if (['document', 'font', 'image', 'stylesheet', 'media'].includes(resourceType)) {
                console.log(`允许资源: ${resourceType} ${request.url().substring(0, 50)}...`);
                request.continue();
              } else {
                console.log(`拦截非必要资源: ${resourceType} ${request.url().substring(0, 50)}...`);
                request.abort();
              }
            } catch (e) {
              console.warn(`请求处理出错: ${e.message}`);
              // 如果请求已经被处理，忽略错误
              if (!e.message.includes('Request is already handled')) {
                throw e;
              }
            }
          };
          
          // 添加监听器
          page.on('request', onRequest);
          
          // 为页面设置标记，避免重复设置拦截
          await page.evaluate(() => {
            window._puppeteer_request_interception_enabled = true;
          }).catch(() => {});
        } else {
          console.log("请求拦截已设置，跳过");
        }
      } catch (e) {
        console.warn(`设置请求拦截失败: ${e.message}，继续执行`);
        // 如果设置失败，尝试关闭拦截
        if (interceptIsSet) {
          try {
            await page.setRequestInterception(false).catch(() => {});
          } catch (_) {}
        }
      }
      
      // 2. 添加样式和脚本优化
      try {
        const optimizationScript = `
          // 禁用外部资源的延迟加载
          document.querySelectorAll('img[loading="lazy"]').forEach(img => img.loading = 'eager');
          // 禁用动画，减少CPU使用
          document.querySelectorAll('*').forEach(el => {
            if (window.getComputedStyle(el).getPropertyValue('animation-name') !== 'none') {
              el.style.animation = 'none';
            }
          });
        `;
        
        // 为HTML内容添加优化
        const enhancedHTML = html.replace('</head>', 
          `<style>
            /* 优化渲染性能的CSS */
            * { animation-duration: 0s !important; transition-duration: 0s !important; }
            img { content-visibility: auto; }
           </style>
           <script>${optimizationScript}</script>
           </head>`
        );
        
        // 3. 设置内容优化级别
        await Promise.race([
          page.setContent(enhancedHTML, {
            waitUntil: ['domcontentloaded', 'networkidle0'],
            timeout: 60000, // 增加到60秒
          }),
          new Promise((_, reject) => setTimeout(() => reject(new Error('设置页面内容超时')), 70000)) // 额外的超时保护
        ]);
      } catch (setContentError) {
        console.error(`设置页面内容失败: ${setContentError.message}`);
        
        // 如果是超时错误，尝试更简单的渲染策略
        if (setContentError.message.includes('timeout')) {
          try {
            console.log('尝试备用渲染策略...');
            
            // 先访问空白页
            await Promise.race([
              page.goto('about:blank', { waitUntil: 'networkidle0', timeout: 5000 }),
              new Promise((_, reject) => setTimeout(() => reject(new Error('导航到空白页超时')), 6000))
            ]);
            
            // 使用更基本的渲染策略
            await Promise.race([
              page.setContent(html, {
                waitUntil: 'domcontentloaded', // 只等待DOM加载完成，不等待网络空闲
                timeout: 60000,
              }),
              new Promise((_, reject) => setTimeout(() => reject(new Error('备用策略设置页面内容超时')), 70000))
            ]);
            
            // 手动等待页面渲染完成
            await page.evaluate(() => {
              return new Promise(resolve => {
                // 低延迟的等待策略
                let checkCount = 0;
                const checkReady = () => {
                  checkCount++;
                  // 如果检查了20次（约2秒）后还未完成，则强制继续
                  if (checkCount > 20) {
                    console.log('强制继续渲染流程');
                    return resolve(true);
                  }
                  
                  // 检查关键元素是否已加载
                  const isReady = 
                    document.readyState === 'complete' || 
                    document.body.getBoundingClientRect().height > 0;
                  
                  if (isReady) {
                    resolve(true);
                  } else {
                    setTimeout(checkReady, 100);
                  }
                };
                checkReady();
              });
            });
          } catch (retryError) {
            console.error(`备用渲染策略也失败: ${retryError.message}`);
            throw new Error(`无法设置页面内容: ${retryError.message}`);
          }
        } else {
          throw setContentError; // 不是超时错误，直接抛出
        }
      }
    } catch (e) {
      console.error(`设置页面内容时出现错误: ${e.message}`);
      throw e;
    }
    performance.setContent = Date.now() - setContentStart;
    console.log(`设置页面内容完成，耗时: ${performance.setContent}ms`);
    
    // 确保所有图片已加载 - 使用超时保护
    console.log('等待图片加载完成...');
    const imageLoadStart = Date.now();
    try {
      await Promise.race([
        page.evaluate(() => {
          return new Promise((resolve) => {
            const images = document.querySelectorAll('img');
            if (images.length === 0) {
              console.log('页面中没有图片');
              return resolve({status: true, message: '页面中没有图片'});
            }
            
            console.log(`需要加载 ${images.length} 张图片`);
            let loadedImages = 0;
            const totalImages = images.length;
            
            // 记录开始时间
            const startTime = Date.now();
            const imageStats = [];
            
            for (let i = 0; i < images.length; i++) {
              const img = images[i];
              const imgStartTime = Date.now();
              const imgInfo = {
                index: i + 1,
                src: img.src,
                startTime: imgStartTime,
                endTime: 0,
                loadTime: 0,
                status: 'pending'
              };
              
              if (img.complete) {
                loadedImages++;
                imgInfo.endTime = Date.now();
                imgInfo.loadTime = imgInfo.endTime - imgInfo.startTime;
                imgInfo.status = 'complete-initial';
                console.log(`图片${i+1}已加载完成: ${img.src}, 进度: ${loadedImages}/${totalImages}`);
              } else {
                img.addEventListener('load', () => {
                  loadedImages++;
                  imgInfo.endTime = Date.now();
                  imgInfo.loadTime = imgInfo.endTime - imgInfo.startTime;
                  imgInfo.status = 'loaded';
                  console.log(`图片${i+1}加载成功: ${img.src}, 耗时: ${imgInfo.loadTime}ms, 进度: ${loadedImages}/${totalImages}`);
                  
                  if (loadedImages === totalImages) {
                    const totalTime = Date.now() - startTime;
                    console.log(`所有图片加载完成, 总耗时: ${totalTime}ms`);
                    resolve({
                      status: true, 
                      totalTime: totalTime,
                      message: `所有${totalImages}张图片加载完成, 总耗时: ${totalTime}ms`,
                      imageStats: imageStats
                    });
                  }
                });
                
                img.addEventListener('error', () => {
                  loadedImages++;
                  imgInfo.endTime = Date.now();
                  imgInfo.loadTime = imgInfo.endTime - imgInfo.startTime;
                  imgInfo.status = 'error';
                  console.error(`图片${i+1}加载失败: ${img.src}, 耗时: ${imgInfo.loadTime}ms, 进度: ${loadedImages}/${totalImages}`);
                  
                  if (loadedImages === totalImages) {
                    const totalTime = Date.now() - startTime;
                    console.log(`所有图片加载尝试完成, 总耗时: ${totalTime}ms, 但有失败项`);
                    resolve({
                      status: true, 
                      totalTime: totalTime,
                      message: `所有${totalImages}张图片加载尝试完成, 总耗时: ${totalTime}ms, 但有${imageStats.filter(img => img.status === 'error').length}张失败`,
                      imageStats: imageStats
                    });
                  }
                });
              }
              
              imageStats.push(imgInfo);
            }
            
            if (loadedImages === totalImages) {
              const totalTime = Date.now() - startTime;
              console.log(`所有图片已经在初始状态下完成加载, 总耗时: ${totalTime}ms`);
              resolve({
                status: true, 
                totalTime: totalTime,
                message: `所有${totalImages}张图片在初始状态下完成加载, 总耗时: ${totalTime}ms`,
                imageStats: imageStats
              });
            }
            
            // 设置超时确保不会无限等待
            setTimeout(() => {
              const elapsedTime = Date.now() - startTime;
              const pendingImages = imageStats.filter(img => img.status === 'pending').length;
              console.log(`图片加载超时, 等待时间: ${elapsedTime}ms, 已加载: ${loadedImages}/${totalImages}, 还有${pendingImages}张未完成`);
              resolve({
                status: false, 
                totalTime: elapsedTime,
                message: `图片加载超时, 等待${elapsedTime}ms后仍有${pendingImages}张未完成`,
                imageStats: imageStats
              });
            }, 5000);
          });
        }),
        new Promise((_, reject) => setTimeout(() => reject(new Error('等待图片加载超时')), 10000)) // 整体超时
      ]).then(result => {
        if (result && typeof result === 'object') {
          console.log(`图片加载结果: ${result.message}`);
        }
      });
    } catch (err) {
      console.warn(`等待图片加载出错: ${err.message}，继续执行后续操作`);
    }
    performance.imageLoad = Date.now() - imageLoadStart;
    console.log(`图片加载检查完成，耗时: ${performance.imageLoad}ms`);
    
    console.log('页面内容设置成功');

    // 预处理HTML，优化渲染 - 两种输出类型的通用预处理
    try {
      // 移除可能影响性能的元素
      await page.evaluate(() => {
        // 移除不必要的动画脚本
        document.querySelectorAll('script').forEach(script => {
          if (script.textContent.includes('animation') || 
              script.textContent.includes('transition') ||
              script.textContent.includes('setTimeout') ||
              script.textContent.includes('setInterval')) {
            script.remove();
          }
        });
        
        // 移除视频元素
        document.querySelectorAll('video, iframe').forEach(el => el.remove());
        
        // 拦截所有媒体查询，确保使用静态布局
        const styleTag = document.createElement('style');
        styleTag.innerHTML = `
          @media all {
            * {
              animation: none !important;
              transition: none !important;
            }
          }
        `;
        document.head.appendChild(styleTag);
        
        // 禁用所有可能的JavaScript动画
        window.requestAnimationFrame = function() {};
        window.cancelAnimationFrame = function() {};
        window.setTimeout = function(fn, delay) { if (delay < 100) fn(); };
        window.setInterval = function() { return 0; };
        
        return true;
      });
      
      // 添加小延迟让页面稳定
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (e) {
      console.warn('预处理页面失败，但继续执行:', e.message);
    }

    return { page, performance };
  } catch (error) {
    console.error(`处理HTML过程中发生错误:`, error);
    
    // 清理资源 - 确保页面不再使用
    if (page) {
      try {
        // 尝试重置页面内容，避免潜在的内存泄漏
        try {
          await Promise.race([
            page.goto('about:blank', { timeout: 3000 }),
            new Promise((_, reject) => setTimeout(() => reject(new Error('导航到空白页超时')), 3500))
          ]).catch(e => {
            console.warn(`重置页面状态失败: ${e.message}`);
          });
        } catch (e) {
          console.warn(`重置页面失败: ${e.message}`);
        }
        
        // 标记页面有问题，避免后续使用
        page._hasIssues = true;
        
        // 释放页面回池中（该方法会检查页面状态并决定是释放还是关闭）
        await browserPool.releasePage(page);
      } catch (e) {
        console.error('释放页面失败:', e);
        try {
          await page.close().catch(closeError => console.error('关闭页面失败:', closeError));
        } catch (closeError) {
          console.error('关闭页面失败:', closeError);
        }
      }
    }
    
    throw error;
  }
}

/**
 * 清理页面并报告性能
 * @param {Object} page - Puppeteer页面对象
 * @param {Object} performance - 性能监控对象
 * @param {string} outputType - 输出类型，如'PDF'或'JPEG'
 * @param {number} conversionTime - 转换操作耗时
 * @param {string} conversionKey - 性能对象中转换操作的键名
 * @returns {Promise<void>}
 */
async function cleanupAndReport(page, performance, outputType, conversionTime, conversionKey) {
  // 将页面重置到空白状态，以便更好地回到池中
  const cleanupStart = Date.now();
  try {
    await Promise.race([
      page.goto('about:blank', { waitUntil: 'networkidle0', timeout: 5000 }),
      new Promise((_, reject) => setTimeout(() => reject(new Error('导航到空白页超时')), 6000))
    ]);
  } catch (error) {
    console.warn(`重置页面到空白状态失败: ${error.message}`);
    // 失败时不影响主逻辑
  }

  // 释放页面回池中
  await browserPool.releasePage(page);
  performance.cleanup = Date.now() - cleanupStart;
  console.log(`页面清理和释放完成，耗时: ${performance.cleanup}ms`);
  console.log('页面已释放回池中');

  // 设置转换时间
  performance[conversionKey] = conversionTime;

  // 计算总时间并输出性能报告
  performance.total = Date.now() - performance.start;
  console.log('----------------------------------------');
  console.log(`${outputType}生成性能报告:`);
  console.log(`总耗时: ${performance.total}ms`);
  console.log(`获取页面: ${performance.getPage}ms (${(performance.getPage/performance.total*100).toFixed(1)}%)`);
  console.log(`设置内容: ${performance.setContent}ms (${(performance.setContent/performance.total*100).toFixed(1)}%)`);
  console.log(`图片加载: ${performance.imageLoad}ms (${(performance.imageLoad/performance.total*100).toFixed(1)}%)`);
  console.log(`${outputType}生成: ${performance[conversionKey]}ms (${(performance[conversionKey]/performance.total*100).toFixed(1)}%)`);
  console.log(`清理释放: ${performance.cleanup}ms (${(performance.cleanup/performance.total*100).toFixed(1)}%)`);
  console.log('----------------------------------------');
}

/**
 * 将HTML转换为PDF
 * @param {string} html - 要转换的HTML内容
 * @param {Object} options - 转换选项
 * @param {Object} options.pdfOptions - 传递给page.pdf()的选项
 * @returns {Promise<Buffer>} - 返回PDF数据的Buffer
 */
async function generatePDF(html, options = {}) {
  const {
    pdfOptions = {},
    savePath = null
  } = options;
  
  let page = null;
  
  try {
    // 使用共享处理函数来处理HTML内容
    const { page: processedPage, performance } = await processHTML(html, options, 'pdf');
    page = processedPage;

    // PDF特定的额外样式
    try {
      await page.evaluate(() => {
        // 为PDF添加打印专用样式
        const printStyleTag = document.createElement('style');
        printStyleTag.innerHTML = `
          @media print {
            body {
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            
            img, svg {
              max-width: 100% !important;
              page-break-inside: avoid !important;
            }
            
            * {
              page-break-inside: auto !important;
            }
          }
        `;
        document.head.appendChild(printStyleTag);
        return true;
      });
    } catch (e) {
      console.warn('添加PDF特定样式失败，但继续执行:', e.message);
    }

    // 生成PDF - 添加超时保护
    console.log('开始生成PDF...');
    const pdfGenerationStart = Date.now();
    
    // 执行PDF生成操作
    const pdf = await Promise.race([
      page.pdf({
        format: 'A4',
        printBackground: true,
        preferCSSPageSize: true,
        scale: 1.0, // 更准确的缩放
        ...pdfOptions // 合并用户提供的PDF选项
      }),
      new Promise((_, reject) => setTimeout(() => reject(new Error('生成PDF超时')), 40000)) // 设置更长的超时
    ]);
    const conversionTime = Date.now() - pdfGenerationStart;
    console.log(`PDF生成成功，耗时: ${conversionTime}ms`);

    // 清理资源并报告性能
    await cleanupAndReport(page, performance, 'PDF', conversionTime, 'pdfGeneration');
    page = null;

    // 可选：保存PDF到本地
    if (savePath) {
      const pdfPath = savePath;
      fs.writeFileSync(pdfPath, pdf);
      console.log(`PDF已保存到: ${pdfPath}`);
    }

    // 将Uint8Array转换为Buffer
    return Buffer.from(pdf);

  } catch (error) {
    console.error('生成PDF过程中发生错误:', error);
    
    // 错误处理已经在processHTML中完成，这里只处理PDF生成后的错误
    if (page && !page.isClosed?.()) {
      try {
        page._hasIssues = true;
        await browserPool.releasePage(page);
      } catch (e) {
        console.error('清理页面资源失败:', e);
      }
    }
    
    throw error;
  }
}

/**
 * 将HTML转换为JPEG图片
 * @param {string} html - 要转换的HTML内容
 * @param {Object} options - 转换选项
 * @param {Object} options.imageOptions - 传递给page.screenshot()的选项
 * @returns {Promise<Buffer>} - 返回图片数据的Buffer
 */
async function generateJPEG(html, options = {}) {
  const {
    imageOptions = {
      type: 'jpeg',
      quality: 60,
      fullPage: true
    },
    savePath = null
  } = options;
  
  let page = null;
  
  try {
    // 使用共享处理函数来处理HTML内容
    const { page: processedPage, performance } = await processHTML(html, options, 'jpeg');
    page = processedPage;

    // 生成JPEG - 添加超时保护
    console.log('开始生成JPEG图片...');
    const screenshotStart = Date.now();
    
    // 执行截图操作
    const jpeg = await Promise.race([
      page.screenshot({
        ...imageOptions,
        omitBackground: !!imageOptions.transparent,
        optimizeForSpeed: true // 添加优化标志
      }),
      new Promise((_, reject) => setTimeout(() => reject(new Error('生成JPEG超时')), 40000)) // 设置更长的超时
    ]);
    const conversionTime = Date.now() - screenshotStart;
    console.log(`JPEG图片生成成功，耗时: ${conversionTime}ms`);

    // 清理资源并报告性能
    await cleanupAndReport(page, performance, 'JPEG', conversionTime, 'screenshot');
    page = null;

    // 可选：保存图片到本地
    if (savePath) {
      const imagePath = savePath;
      fs.writeFileSync(imagePath, jpeg);
      console.log(`图片已保存到: ${imagePath}`);
    }

    // 将Uint8Array转换为Buffer
    return Buffer.from(jpeg);

  } catch (error) {
    console.error('生成JPEG图片过程中发生错误:', error);
    
    // 错误处理已经在processHTML中完成，这里只处理JPEG生成后的错误
    if (page && !page.isClosed?.()) {
      try {
        page._hasIssues = true;
        await browserPool.releasePage(page);
      } catch (e) {
        console.error('清理页面资源失败:', e);
      }
    }
    
    throw error;
  }
}

/**
 * 带重试的HTML到PDF转换
 * @param {string} html - 要转换的HTML内容
 * @param {Object} options - 转换选项
 * @returns {Promise<Buffer>} - 返回PDF数据的Buffer
 */
async function generatePDFWithRetry(html, options = {}) {
  let retryCount = 0;
  let lastError = null;

  while (retryCount < MAX_RETRIES) {
    try {
      console.log(`第 ${retryCount + 1} 次尝试生成PDF...`);
      const pdfBuffer = await generatePDF(html, options); // 传递选项参数
      return pdfBuffer; // 如果成功，直接返回PDF
    } catch (error) {
      lastError = error;
      retryCount++;
      console.error(`第 ${retryCount} 次尝试失败:`, error.message);

      if (retryCount < MAX_RETRIES) {
        const delay = RETRY_DELAY * retryCount; // 递增延迟
        console.log(`等待 ${delay}ms 后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay)); // 延迟后重试
      }
    }
  }
  
  throw new Error(`PDF生成失败，已尝试 ${MAX_RETRIES} 次: ${lastError?.message}`); // 达到最大重试次数，抛出错误
}

/**
 * 带重试的HTML到JPEG转换
 * @param {string} html - 要转换的HTML内容
 * @param {Object} options - 转换选项
 * @returns {Promise<Buffer>} - 返回图片数据的Buffer
 */
async function generateJPEGWithRetry(html, options = {}) {
  let retryCount = 0;
  let lastError = null;

  while (retryCount < MAX_RETRIES) {
    try {
      console.log(`第 ${retryCount + 1} 次尝试生成JPEG图片...`);
      const jpegBuffer = await generateJPEG(html, options);
      return jpegBuffer;
    } catch (error) {
      lastError = error;
      retryCount++;
      console.error(`第 ${retryCount} 次尝试失败:`, error.message);

      if (retryCount < MAX_RETRIES) {
        const delay = RETRY_DELAY * retryCount; // 递增延迟
        console.log(`等待 ${delay}ms 后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  throw new Error(`JPEG图片生成失败，已尝试 ${MAX_RETRIES} 次: ${lastError?.message}`);
}

// 在模块导出中添加新的方法
module.exports = {
    generatePDF,
    generatePDFWithRetry,
    generateJPEG,
    generateJPEGWithRetry
}; 