require('dotenv').config(); // 在文件顶部加载 .env 文件
// const chromium = require('@sparticuz/chromium');
const puppeteer = require('puppeteer-core');
const path = require('path');
const fs = require('fs');

// 浏览器实例池
class BrowserPool {
  constructor(options = {}) {
    this.maxPoolSize = options.maxPoolSize || 3; // 最大浏览器实例数
    this.minPoolSize = options.minPoolSize || 1; // 最小浏览器实例数（保持活跃）
    this.idleTimeout = options.idleTimeout || 60000; // 空闲超时 (60秒)
    this.pool = []; // 存储浏览器实例
    this.launching = {}; // 正在启动的浏览器实例
    this.browserOptions = options.browserOptions || {}; // 浏览器启动选项
    
    // 注册定期检查，确保保持最小实例数
    this.maintenanceInterval = setInterval(() => this.ensureMinInstances(), 30000);
    
    // 启动时预热浏览器实例
    if (options.preloadOnStart !== false) {
      this.preloadInstances();
    }
  }

  // 预热浏览器实例
  async preloadInstances() {
    console.log(`BrowserPool: 预热浏览器实例，目标数量: ${this.minPoolSize}`);
    try {
      const preloadPromises = [];
      for (let i = 0; i < this.minPoolSize; i++) {
        preloadPromises.push(this.createBrowserAndAddToPool());
      }
      await Promise.all(preloadPromises);
      console.log(`BrowserPool: 完成预热，当前池大小: ${this.pool.length}`);
    } catch (error) {
      console.error('BrowserPool: 预热浏览器实例失败', error);
    }
  }

  // 确保池中至少有最小数量的实例
  async ensureMinInstances() {
    const activeCount = this.pool.length + Object.keys(this.launching).length;
    if (activeCount < this.minPoolSize) {
      console.log(`BrowserPool: 检测到实例数量低于最小值，当前: ${activeCount}，最小: ${this.minPoolSize}`);
      try {
        for (let i = activeCount; i < this.minPoolSize; i++) {
          await this.createBrowserAndAddToPool();
        }
      } catch (error) {
        console.error('BrowserPool: 确保最小实例数时出错', error);
      }
    }
  }

  // 创建浏览器并直接添加到池中
  async createBrowserAndAddToPool() {
    const browser = await this.createBrowser();
    this.pool.push(browser);
    return browser;
  }

  // 获取一个可用的浏览器实例
  async getBrowser() {
    console.log(`BrowserPool: 请求浏览器实例，当前池大小: ${this.pool.length}/${this.maxPoolSize}`);
    
    // 检查池中是否有可用实例
    if (this.pool.length > 0) {
      const browser = this.pool.shift();
      
      // 清除原有的空闲计时器
      if (browser._idleTimer) {
        clearTimeout(browser._idleTimer);
        browser._idleTimer = null;
      }
      
      // 检查浏览器实例是否已断开连接
      try {
        if (!browser.isConnected()) {
          console.log('BrowserPool: 发现断开连接的浏览器实例，创建新实例');
          return this.createBrowser();
        }
      } catch (error) {
        console.error('BrowserPool: 检查浏览器连接时出错', error);
        return this.createBrowser();
      }
      
      console.log('BrowserPool: 返回现有浏览器实例');
      return browser;
    }
    
    // 没有可用实例且未达到最大池大小，创建新实例
    if (this.pool.length + Object.keys(this.launching).length < this.maxPoolSize) {
      return this.createBrowser();
    }
    
    // 达到最大池大小，等待实例释放
    console.log('BrowserPool: 达到最大池大小，等待可用实例');
    return new Promise((resolve) => {
      const checkInterval = setInterval(() => {
        if (this.pool.length > 0) {
          clearInterval(checkInterval);
          resolve(this.getBrowser());
        }
      }, 100);
    });
  }

  // 创建新的浏览器实例
  async createBrowser() {
    const id = Date.now().toString();
    console.log(`BrowserPool: 创建新的浏览器实例 ID: ${id}`);
    
    this.launching[id] = true;
    
    let browser;
    let executablePath; // 声明变量

    try {
      // 优先从环境变量读取路径
      const customPath = process.env.CHROMIUM_PATH;

      if (customPath) {
        // 如果设置了环境变量，验证路径
        console.log(`尝试使用环境变量 CHROMIUM_PATH: ${customPath}`);
        if (fs.existsSync(customPath)) {
          executablePath = customPath;
          console.log(`使用环境变量指定的 Chromium 路径: ${executablePath}`);
        } else {
          console.error(`错误：环境变量 CHROMIUM_PATH 指定的路径无效: ${customPath}`);
          // 如果环境变量路径无效，可以选择抛出错误或回退
          // 这里选择回退到 sparticuz
          console.warn(`回退到使用 @sparticuz/chromium 自动下载的路径...`);
          // executablePath = await chromium.executablePath();
        }
      } else {
        // 如果未设置环境变量，使用 sparticuz 的路径
        console.log(`环境变量 CHROMIUM_PATH 未设置，使用 @sparticuz/chromium 自动下载的路径...`);
        // executablePath = await chromium.executablePath();
      }
      
      // 再次检查最终确定的路径是否存在 (主要针对 sparticuz 可能返回无效路径的情况)
      if (!fs.existsSync(executablePath)) {
        console.error(`错误：最终确定的 Chromium 路径无效: ${executablePath}`);
        throw new Error(`Chromium executable not found at ${executablePath}`);
      }

      browser = await puppeteer.launch({
        args: [
          // ...chromium.args, // 仍然可以使用 sparticuz 的推荐参数
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-gpu'
          // ... 其他必要的 args ...
        ],
        executablePath: executablePath, // 使用最终确定的路径
        headless: true, // 或者 'shell'
        // ... 保留其他必要的 browserOptions ...
        protocolTimeout: this.browserOptions.protocolTimeout || 60000,
        timeout: this.browserOptions.timeout || 30000,
        dumpio: !!process.env.DEBUG?.includes('puppeteer:'),
      });

      // 监听浏览器关闭事件
      browser.on('disconnected', () => {
        console.log(`BrowserPool: 浏览器实例断开连接 ID: ${id}`);
        // 从池中移除断开连接的实例
        this.pool = this.pool.filter(b => b !== browser);
        // 确保维持最小实例数
        this.ensureMinInstances();
      });

      // 为浏览器实例添加ID
      browser._id = id;
    } catch (error) {
      console.error(`BrowserPool: 创建浏览器实例失败 ID: ${id}`, error);
      delete this.launching[id];
      throw error; // 重新抛出错误，让上层处理
    } finally {
      // 确保即使出错也移除启动标记
      delete this.launching[id];
    }
    
    return browser;
  }

  // 释放浏览器实例回池中
  async releaseBrowser(browser) {
    // 防止浏览器已关闭的情况
    try {
      if (!browser || !browser.isConnected()) {
        console.log('BrowserPool: 尝试释放已关闭的浏览器实例，忽略');
        // 确保维持最小实例数
        this.ensureMinInstances();
        return;
      }
    } catch (error) {
      console.error('BrowserPool: 检查浏览器连接状态时出错', error);
      // 确保维持最小实例数
      this.ensureMinInstances();
      return;
    }
    
    // 清理浏览器中的所有页面
    try {
      const pages = await browser.pages();
      await Promise.all(pages.map(page => {
        try {
          return page.close();
        } catch (e) {
          console.error('BrowserPool: 关闭页面失败', e);
          return Promise.resolve();
        }
      }));
    } catch (error) {
      console.error('BrowserPool: 清理页面时出错', error);
      
      // 如果清理页面失败，尝试关闭浏览器
      try {
        await browser.close();
      } catch (e) {
        console.error('BrowserPool: 关闭故障浏览器时出错', e);
      }
      
      // 确保维持最小实例数
      this.ensureMinInstances();
      return;
    }
    
    // 将浏览器放回池中
    this.pool.push(browser);
    console.log(`BrowserPool: 浏览器实例释放回池中，当前池大小: ${this.pool.length}/${this.maxPoolSize}`);
    
    // 设置空闲超时 - 只有当池中的实例数大于最小值时才设置超时关闭
    if (this.pool.length > this.minPoolSize) {
      browser._idleTimer = setTimeout(async () => {
        // 如果浏览器仍在池中且超时，且池大小仍然超过最小值，关闭它
        if (this.pool.includes(browser) && this.pool.length > this.minPoolSize) {
          this.pool = this.pool.filter(b => b !== browser);
          console.log(`BrowserPool: 关闭空闲浏览器实例，当前池大小: ${this.pool.length}/${this.maxPoolSize}`);
          
          try {
            await browser.close();
          } catch (error) {
            console.error('BrowserPool: 关闭空闲浏览器时出错', error);
          }
        }
      }, this.idleTimeout);
    } else {
      console.log(`BrowserPool: 保持浏览器实例活跃，当前池大小(${this.pool.length})不超过最小值(${this.minPoolSize})`);
    }
  }

  // 关闭所有浏览器实例
  async closeAll() {
    // 先清除维护定时器
    if (this.maintenanceInterval) {
      clearInterval(this.maintenanceInterval);
      this.maintenanceInterval = null;
    }
    
    console.log(`BrowserPool: 关闭所有浏览器实例，当前数量: ${this.pool.length}`);
    
    // 关闭所有浏览器实例
    const closePromises = this.pool.map(async (browser) => {
      try {
        if (browser._idleTimer) {
          clearTimeout(browser._idleTimer);
          browser._idleTimer = null;
        }
        
        await browser.close();
      } catch (error) {
        console.error('BrowserPool: 关闭浏览器时出错', error);
      }
    });
    
    await Promise.all(closePromises);
    this.pool = [];
  }
}

// 创建浏览器池实例
const browserPool = new BrowserPool({
  maxPoolSize: 3, // 最大3个并发浏览器实例
  minPoolSize: 1, // 最小1个浏览器实例保持活跃
  idleTimeout: 60000, // 60秒空闲超时
  preloadOnStart: true // 启动时预热
});

const MAX_RETRIES = 3; // 最大重试次数
const RETRY_DELAY = 1000; // 重试延迟（毫秒）

/**
 * 将HTML转换为PDF
 * @param {string} html - 要转换的HTML内容
 * @param {Object} options - 转换选项
 * @param {Object} options.pdfOptions - 传递给page.pdf()的选项
 * @returns {Promise<Buffer>} - 返回PDF数据的Buffer
 */
async function generatePDF(html, options = {}) {
  let browser = null;
  let page = null;
  
  const {
    // baseUrl = "http://localhost:3001/static",
    pdfOptions = {}
  } = options;
  
  try {
    console.log('generatePDF 开始执行...');
    console.log('收到HTML内容长度:', html?.length);

    console.log('准备获取浏览器实例...');
    browser = await browserPool.getBrowser();
    console.log('获取浏览器实例成功');

    page = await browser.newPage();
    await page.setViewport({
      width: 794,
      height: 1123,
      deviceScaleFactor: 2,
    });
    console.log('新页面创建成功');

    // 设置内容
    console.log('设置页面内容...');
    
    await page.setContent(html, {
      waitUntil: ['networkidle0', 'load', 'domcontentloaded'],
      timeout: 30000,
    //   baseUrl // 使用传入的baseUrl参数
    });
    
    // 设置请求监听（用于调试）
    await page.setRequestInterception(true);

    page.on('pageerror', error => console.error('Page error:', error));
    page.on('response', response => {
        if (response.status() >= 400) {
            console.error(`请求失败: ${response.url()} ${response.status()}`);
        }
    });

    page.on('request', (request) => {
      console.log(`加载资源: ${request.resourceType()}: ${request.url()}`);
      request.continue();
    });
    
    page.on('requestfailed', (request) => {
      console.error(`资源加载失败: ${request.url()}, 错误: ${request.failure().errorText}`);
    });
    
    // 确保所有图片已加载
    console.log('等待图片加载完成...');
    await page.evaluate(() => {
      return new Promise((resolve) => {
        const images = document.querySelectorAll('img');
        if (images.length === 0) {
          console.log('页面中没有图片');
          return resolve();
        }
        
        console.log(`需要加载 ${images.length} 张图片`);
        let loadedImages = 0;
        const totalImages = images.length;
        
        for (let img of images) {
          if (img.complete) {
            loadedImages++;
            console.log(`图片已加载完成: ${img.src}, 进度: ${loadedImages}/${totalImages}`);
          } else {
            img.addEventListener('load', () => {
              loadedImages++;
              console.log(`图片加载成功: ${img.src}, 进度: ${loadedImages}/${totalImages}`);
              if (loadedImages === totalImages) resolve();
            });
            
            img.addEventListener('error', () => {
              loadedImages++;
              console.error(`图片加载失败: ${img.src}, 进度: ${loadedImages}/${totalImages}`);
              if (loadedImages === totalImages) resolve();
            });
          }
        }
        
        if (loadedImages === totalImages) {
          console.log('所有图片已加载完成');
          resolve();
        }
        
        // 设置超时确保不会无限等待
        setTimeout(() => {
          console.log(`图片加载超时，已加载: ${loadedImages}/${totalImages}`);
          resolve();
        }, 5000);
      });
    });
    
    console.log('页面内容设置成功');

    // 生成PDF
    console.log('开始生成PDF...');
    const pdf = await page.pdf({
      format: 'A4',
      printBackground: true,
      preferCSSPageSize: true,
    //   margin: {
    //     top: '20mm',
    //     right: '20mm',
    //     bottom: '20mm',
    //     left: '20mm'
    //   },
      ...pdfOptions // 合并用户提供的PDF选项
    });
    console.log('PDF生成成功');

    // 关闭页面
    await page.close();
    page = null;
    
    // 释放浏览器实例回池中而不是关闭
    await browserPool.releaseBrowser(browser);
    browser = null;
    console.log('浏览器实例已释放回池中');

    // 可选：保存PDF到本地 (现在只在明确需要时才执行)
    if (options.savePath) {
      const pdfPath = options.savePath || path.join(__dirname, 'resume.pdf');
      fs.writeFileSync(pdfPath, pdf);
      console.log(`PDF已保存到: ${pdfPath}`);
    }

    // 将Uint8Array转换为Buffer
    const buffer = Buffer.from(pdf);

    return buffer;

  } catch (error) {
    console.error('生成PDF过程中发生错误:', error);
    console.error('错误堆栈:', error.stack);
    
    // 清理资源
    if (page) {
      try {
        await page.close();
      } catch (e) {
        console.error('关闭页面失败:', e);
      }
    }
    
    if (browser) {
      try {
        // 如果出错，我们尝试关闭浏览器实例而不是释放回池中
        await browser.close();
      } catch (e) {
        console.error('关闭浏览器失败:', e);
      }
    }
    
    throw error;
  }
}

/**
 * 带重试的HTML到PDF转换
 * @param {string} html - 要转换的HTML内容
 * @param {Object} options - 转换选项
 * @returns {Promise<Buffer>} - 返回PDF数据的Buffer
 */
async function generatePDFWithRetry(html, options = {}) {
  let retryCount = 0;

  while (retryCount < MAX_RETRIES) {
    try {
      console.log(`第 ${retryCount + 1} 次尝试生成PDF...`);
      const pdfBuffer = await generatePDF(html, options); // 传递选项参数
      return pdfBuffer; // 如果成功，直接返回PDF
    } catch (error) {
      retryCount++;
      console.error(`第 ${retryCount} 次尝试失败:`, error.message);

      if (retryCount < MAX_RETRIES) {
        console.log(`等待 ${RETRY_DELAY}ms 后重试...`);
        await new Promise(resolve => setTimeout(resolve, RETRY_DELAY)); // 延迟后重试
      } else {
        throw new Error(`PDF生成失败，已尝试 ${MAX_RETRIES} 次: ${error.message}`); // 达到最大重试次数，抛出错误
      }
    }
  }
}

// 进程退出前清理资源
process.on('exit', async () => {
  try {
    await browserPool.closeAll();
  } catch (error) {
    console.error('退出时关闭浏览器池失败:', error);
  }
});

// 处理意外异常
process.on('uncaughtException', async (error) => {
  console.error('未捕获的异常:', error);
  try {
    await browserPool.closeAll();
  } catch (e) {
    console.error('关闭浏览器池失败:', e);
  }
  process.exit(1);
});

/**
 * 将HTML转换为JPEG图片
 * @param {string} html - 要转换的HTML内容
 * @param {Object} options - 转换选项
 * @param {Object} options.imageOptions - 传递给page.screenshot()的选项
 * @returns {Promise<Buffer>} - 返回图片数据的Buffer
 */
async function generateJPEG(html, options = {}) {
  let browser = null;
  let page = null;
  
  const {
    imageOptions = {
      type: 'jpeg',
      quality: 90,
      fullPage: true
    }
  } = options;
  
  try {
    console.log('generateJPEG 开始执行...');
    console.log('收到HTML内容长度:', html?.length);

    console.log('准备获取浏览器实例...');
    browser = await browserPool.getBrowser();
    console.log('获取浏览器实例成功');

    page = await browser.newPage();
    await page.setViewport({
      width: 794,
      height: 1123,
      deviceScaleFactor: 2,
    });
    console.log('新页面创建成功');

    // 设置内容
    console.log('设置页面内容...');
    
    await page.setContent(html, {
      // waitUntil: ['networkidle0', 'load', 'domcontentloaded'],
      timeout: 30000,
    });
    
    // 设置请求监听（用于调试）
    // await page.setRequestInterception(true);

    // page.on('pageerror', error => console.error('Page error:', error));
    // page.on('response', response => {
    //     if (response.status() >= 400) {
    //         console.error(`请求失败: ${response.url()} ${response.status()}`);
    //     }
    // });

    // page.on('request', (request) => {
    //   console.log(`加载资源: ${request.resourceType()}: ${request.url()}`);
    //   request.continue();
    // });
    
    // page.on('requestfailed', (request) => {
    //   console.error(`资源加载失败: ${request.url()}, 错误: ${request.failure().errorText}`);
    // });
    
    // 确保所有图片已加载
    console.log('等待图片加载完成...');
    await page.evaluate(() => {
      return new Promise((resolve) => {
        const images = document.querySelectorAll('img');
        if (images.length === 0) {
          console.log('页面中没有图片');
          return resolve();
        }
        
        console.log(`需要加载 ${images.length} 张图片`);
        let loadedImages = 0;
        const totalImages = images.length;
        
        for (let img of images) {
          if (img.complete) {
            loadedImages++;
            console.log(`图片已加载完成: ${img.src}, 进度: ${loadedImages}/${totalImages}`);
          } else {
            img.addEventListener('load', () => {
              loadedImages++;
              console.log(`图片加载成功: ${img.src}, 进度: ${loadedImages}/${totalImages}`);
              if (loadedImages === totalImages) resolve();
            });
            
            img.addEventListener('error', () => {
              loadedImages++;
              console.error(`图片加载失败: ${img.src}, 进度: ${loadedImages}/${totalImages}`);
              if (loadedImages === totalImages) resolve();
            });
          }
        }
        
        if (loadedImages === totalImages) {
          console.log('所有图片已加载完成');
          resolve();
        }
        
        // 设置超时确保不会无限等待
        setTimeout(() => {
          console.log(`图片加载超时，已加载: ${loadedImages}/${totalImages}`);
          resolve();
        }, 5000);
      });
    });
    
    console.log('页面内容设置成功');

    // 生成JPEG图片
    console.log('开始生成JPEG图片...');
    const jpeg = await page.screenshot({
      ...imageOptions
    });
    console.log('JPEG图片生成成功');

    // 关闭页面
    await page.close();
    page = null;
    
    // 释放浏览器实例回池中而不是关闭
    await browserPool.releaseBrowser(browser);
    browser = null;
    console.log('浏览器实例已释放回池中');

    // // 可选：保存图片到本地
    // if (options.savePath) {
    const imagePath = options.savePath || path.join(__dirname, 'resume.jpeg');
    fs.writeFileSync(imagePath, jpeg);
    console.log(`图片已保存到: ${imagePath}`);
    // }

    // 将Uint8Array转换为Buffer
    const buffer = Buffer.from(jpeg);

    return buffer;

  } catch (error) {
    console.error('生成JPEG图片过程中发生错误:', error);
    console.error('错误堆栈:', error.stack);
    
    // 清理资源
    if (page) {
      try {
        await page.close();
      } catch (e) {
        console.error('关闭页面失败:', e);
      }
    }
    
    if (browser) {
      try {
        // 如果出错，我们尝试关闭浏览器实例而不是释放回池中
        await browser.close();
      } catch (e) {
        console.error('关闭浏览器失败:', e);
      }
    }
    
    throw error;
  }
}

/**
 * 带重试的HTML到JPEG转换
 * @param {string} html - 要转换的HTML内容
 * @param {Object} options - 转换选项
 * @returns {Promise<Buffer>} - 返回图片数据的Buffer
 */
async function generateJPEGWithRetry(html, options = {}) {
  let retryCount = 0;

  while (retryCount < MAX_RETRIES) {
    try {
      console.log(`第 ${retryCount + 1} 次尝试生成JPEG图片...`);
      const jpegBuffer = await generateJPEG(html, options);
      return jpegBuffer;
    } catch (error) {
      retryCount++;
      console.error(`第 ${retryCount} 次尝试失败:`, error.message);

      if (retryCount < MAX_RETRIES) {
        console.log(`等待 ${RETRY_DELAY}ms 后重试...`);
        await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
      } else {
        throw new Error(`JPEG图片生成失败，已尝试 ${MAX_RETRIES} 次: ${error.message}`);
      }
    }
  }
}

// 在模块导出中添加新的方法
module.exports = {
    generatePDF,
    generatePDFWithRetry,
    generateJPEG,
    generateJPEGWithRetry,
    browserPool
}; 