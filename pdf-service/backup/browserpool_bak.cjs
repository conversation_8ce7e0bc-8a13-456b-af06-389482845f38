const puppeteer = require('puppeteer-core');
const { v4: uuidv4 } = require('uuid');
const { Mutex } = require('async-mutex');
const fs = require('fs');




const chromePath = '/home/<USER>/.cache/puppeteer/chrome-headless-shell/linux-136.0.7103.92/chrome-headless-shell-linux64/chrome-headless-shell';

// execPath = puppeteer.executablePath();
// console.log(execPath);


// (async () => {
//   const browser = await puppeteer.launch({
//     headless: true,
//     args: ['--no-sandbox'],
//     executablePath: chromePath
//   });
//   const page = await browser.newPage();
//   await page.goto('https://example.com');
//   await page.screenshot({ path: 'example.png' });
//   await browser.close();
//   console.log('Done');
// })();


class Browser {
  constructor(id, browserOptions) {
    this.id = id;
    this.browser = null;
    this.pages = new Map(); // [pageId] => { page, createdAt }
    this.usingPages = 0;    // 当前正在使用的页面数
    this.totalPages = 0;    // 总创建页面数（用于重启判断）
    this.status = 'initializing'; // initializing | ready | restarting | error
    this.browserOptions = browserOptions;
    this.pageCreationTime = Date.now();
    // this.mutex = new Mutex();
    this.restartTimeoutId = null;
    this.timeToRestartBrowser = 30000;
    this.timeoutDefaultNavigation = 30000;
    this.timeoutDefault = 30000;
  }

  async initialize() {
    try {
      const startTime = Date.now();
      this.browser = await puppeteer.launch(this.browserOptions);
      this.status = 'ready';
      console.log(`Browser ${this.id} initialize time: ${Date.now() - startTime}ms`);
      return this;
    } catch (error) {
      this.status = 'error';
      // throw error;
    }
  }

  async getPage() {
    if (this.status !== 'ready') {
      throw new Error(`Browser ${this.id} is not ready`);
    }

    const pageId = uuidv4();
    const page = await this.browser.newPage();
    this.pages.set(pageId, { page, createdAt: Date.now() });
    
    // 设置页面超时自动关闭
    page.setDefaultNavigationTimeout(this.timeoutDefaultNavigation);
    page.setDefaultTimeout(this.timeoutDefault);
    
    this.usingPages++;
    this.totalPages++;
    
    return { page, pageId, browserId: this.id };
  }

  async releasePage(pageId) {
    const pageInfo = this.pages.get(pageId);
    if (!pageInfo) return;

    try {
      await pageInfo.page.close();
    } catch (e) {
      console.error(`Error closing page: ${e.message}`);
    }
    
    this.pages.delete(pageId);
    this.usingPages--;
  }

  async close() {
    await this.browser.close();
    this.status = 'closed';
  }

  async checkHealth() {
    // TODO, 更加实际的检查
    const status = await this.browser.version();
    return {
      isAlive: status !== undefined,
      creationTime: this.pageCreationTime,
      totalPages: this.totalPages
    };
  }

  async restart() {
    if (this.restartTimeoutId) clearTimeout(this.restartTimeoutId);
    // this.status = 'restarting';
    
    this.restartTimeoutId = setTimeout(async () => {
      try {
        await this.close();
        await this.initialize();
        this.status = 'ready';
        this.timeToRestartBrowser = 0;
      } catch (error) {
        this.status = 'error';
        throw error;
      }
    }, this.timeToRestartBrowser);
  }
}




// BrowserPool 类
class BrowserPool {
  constructor(options) {
    this.config = {
      initialSize: 3,
      maxBrowsers: 5,
      maxPagesPerBrowser: 20,
      avgUsageThreshold: 15,
      intervalHealthCheckCreateNewBrowser: 30000,
      intervalWarnLoadCreateNewBrowser: 30000,
      // pageTimeout: 30000,
      // restartTimeout: 30000,
      ...options
    };

    this.browsers = new Map();
    // this.available = new Map();
    // this.mutex = new Mutex();
    // this.queue = [];
  }
  
  async initializePool() {
    await this.createBrowsers(this.config.initialSize);
    // await this.initializePool();
    await this.maintenanceRestart();
    await this.maintenanceOverThresholdCreateBrowser();
  }

  async createBrowsers(count) {
    const promises = [];
    for (let i = 0; i < count; i++) {
      promises.push(this.createBrowser());
    }
    await Promise.all(promises);
  }
  async createBrowser() {
    if (this.browsers.size >= this.config.maxBrowsers) return;

    const id = uuidv4();
    const browser = new Browser(id, this.config.browserOptions);
    await browser.initialize();
    this.browsers.set(id, browser);
    // if (browser.status === 'ready') {
    //   this.addToAvailable(id);
    // }
    return id;
  }
  // addToAvailable(id) {
  //   if (!this.available.has(id)) {
  //     this.available.set(id, Date.now());
  //   }
  // }

  async restartBrowser(browserId) {
    try {
      await this.browsers.get(browserId).restart();
      // this.addToAvailable(browserId);
    } catch (error) {
      console.error(`Restart failed for browser ${browserId}:`, error);
      this.browsers.delete(browserId);
      this.createBrowser();
    }
  }

  async closeBrowser(browserId) {
    const browser = this.browsers.get(browserId);
    if (browser) {
      await browser.close();
      this.browsers.delete(browserId);
    }
  }

  ////////////////////////////////////////////////////////////////////////////////////

  selectBrowser(candidates) {
    // 加权随机选择：使用率越低的浏览器被选中的概率越高
  //   const totalWeight = candidates.reduce((sum, b) => sum + (10 - b.usingPages), 0);
  //   let random = Math.random() * totalWeight;
    
  //   for (const browser of candidates) {
  //     random -= (10 - browser.usingPages);
  //     if (random <= 0) return browser;
  //   }
  //   return candidates[0];
  // }
    const totalWeight = candidates.reduce((sum, b) => {
      const weight = Math.max(0, 10 - b.usingPages);
      return sum + weight;
    }, 0);
    
    let random = Math.random() * totalWeight;
    for (const browser of candidates) {
      const weight = Math.max(0, 10 - browser.usingPages);
      random -= weight;
      if (random <= 0) return browser;
    }
    return candidates[0];
  }

  async getPage() {
    try {
      // 优先选择空闲浏览器
      const candidates = Array.from(this.browsers.values())
        .filter(b => b.status === 'ready');
      
      if (candidates.length > 0) {
        const browser = this.selectBrowser(candidates);
        return await browser.getPage();
      } else {
        // 创建新浏览器
        console.log(`Browser pool getPage: 没有可用浏览器，创建新浏览器`);
        const newBrowserId = await this.createBrowser();
        const newBrowser = this.browsers.get(newBrowserId);
        return await newBrowser.getPage();
      }

    } catch (error) {
      console.error('Browser pool getPage error:', error);
      throw error;
    }
  }

  async releasePage({ pageId, browserId }) { // 添加 browserId 参数
    const browser = this.browsers.get(browserId);
    if (!browser) return;
    await browser.releasePage(pageId);
  }


  // markForRestart(browserId) {
  //   const browser = this.browsers.get(browserId);
  //   if (browser.status !== 'to-restart') {
  //     browser.status = 'to-restart';
  //     browser.toRestartTime = Date.now();
  //     // this.restartBrowser(browserId);
  //   }
  // }




  calculateAvgUsage() {
    if (this.browsers.size === 0) return 0;

    const total = Array.from(this.browsers.values())
      .reduce((sum, b) => sum + b.usingPages, 0);
    return total / this.browsers.size;
  }

  async maintenanceRestart() {
    setInterval(async () => {
      // const release = await this.mutex.acquire();
      try {
        // 检查过期浏览器
        const now = Date.now();
        for (const [id, browser] of this.browsers) {
          const health = await browser.checkHealth();

          // 1小时重启
          const isStale = browser.status === 'ready' && now - browser.pageCreationTime > 3600000;
          // 次数过多, 卡死
          const isFull = health.totalPages >= this.config.maxPagesPerBrowser;
          // 无法连接
          const isDead = !health.isAlive;

          if (isStale || isFull || isDead) {
            this.restartBrowser(id);
          }
          // // 时间过长
          // if (
          //   (browser.status === 'ready' && now - browser.pageCreationTime > 3600000) || 
          //   (health.totalPages >= this.config.maxPagesPerBrowser || !health.isAlive)
          // ) { 
          //   // this.markForRestart(id);
          //   this.restartBrowser(id);
          // }
        }

        // 清理排队
        // this.queue = this.queue.filter(ts => Date.now() - ts < 5000);
      } catch (e) {
        console.error(e);
      }
    }, this.config.intervalHealthCheckCreateNewBrowser);
  }

  // check status, then restart browser
  // async maintenanceRestartBrowser() {
  //   setInterval(async () => {
  //     try {
  //       for (const [id, browser] of this.browsers) {
  //         if (
  //           (browser.status === 'to-restart' && browser.usingPages === 0) ||
  //           (browser.status === 'to-restart' && Date.now() - browser.toRestartTime > this.config.restartTimeout)
  //         )  {
  //           await this.restartBrowser(id);
  //         }
  //       }
  //     } catch (error) {
  //       console.error('Browser pool maintenanceRestartBrowser error:', error);
  //     }
  //   }, this.config.healthCheckInterval);
  // }

  // check usage , if over threshold , create new browser
  async maintenanceOverThresholdCreateBrowser() {
    setInterval(async () => {
      try {
        // 弹性扩容
        const avgUsage = this.calculateAvgUsage();
        console.log(`目前浏览器 ${this.browsers.size} 个，平均使用 ${avgUsage} 个`);

        // 如果平均使用率超过阈值，则新开一个浏览器
        if (avgUsage > this.config.avgUsageThreshold && 
            this.browsers.size < this.config.maxBrowsers) {
          await this.createBrowser();
        }
      } catch (error) {
        console.error('Browser pool maintenanceOverThresholdCreateBrowser error:', error);
      }
    }, this.config.intervalWarnLoadCreateNewBrowser);
  }

  closeAllBrowsers() {
    for (const [id, browser] of this.browsers) {
      browser.close();
      console.log(`Browser ${id} closed`);
    }
    this.browsers.clear();
  }
}

// export module
module.exports = {
  Browser,
  BrowserPool,
  chromePath
};





















//////////////////////////  test 
// 使用示例
// const browser = new Browser(id='test',
//   browserOptions= {
//     headless: true,
//     args: ['--no-sandbox'],
//     executablePath: chromePath
//   }
// );



// let pagesNum = browser.pages.size;
// console.log(pagesNum);

// htmlContent = fs.readFileSync('resume.html', 'utf8');

// (async () => {
//   await browser.initialize();
//   total_start_time = Date.now();

//   await browser.initialize();
//   const { page, pageId, browserId } = await browser.getPage();

//   time_start_page = Date.now();
//   pagesNum = browser.pages.size;
//   console.log(pagesNum);


//   // const { page, browserId } = await pool.getPage();
//   // if page.
//   await page.setContent(htmlContent);
//   await page.screenshot({ path: 'example.png' });
  
//   console.log(`总耗时：${Date.now() - total_start_time}ms`);
//   console.log(`页面耗时：${Date.now() - time_start_page}ms`);
//   await browser.releasePage(pageId);
//   pagesNum = browser.pages.size;
//   console.log(pagesNum);

//   const { isAlive, creationTime, totalPages } = await browser.checkHealth();
//   console.log(`浏览器健康状态：${isAlive ? '正常' : '异常'}`);
//   console.log(`浏览器创建时间：${creationTime}`);
//   console.log(`浏览器打开页面数：${totalPages}`);

//   // restart browser
//   await browser.restart();
//   // console.log('关闭浏览器');
//   console.log(`{browser.status}=${browser.status}`);

// }) ();




/////// browserpool test
// const browserpool = new BrowserPool({
//   maxTotalUsage: 100,
//   maxAvgUsage: 20,
//   browserOptions: {
//     headless: true,
//     args: ['--no-sandbox'],
//     executablePath: chromePath
//   }
// });


// (async () => {
//   await browserpool.initializePool();
//   const { page, pageId, browserId } = await browserpool.getPage();
//   await page.setContent('<h1>Hello, world!</h1>');
//   await page.screenshot({ path: 'example.png' });
//   await browserpool.releasePage({ pageId, browserId });
//   console.log('done');
// })();

